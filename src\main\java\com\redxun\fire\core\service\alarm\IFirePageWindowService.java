package com.redxun.fire.core.service.alarm;

import com.redxun.common.base.entity.JsonPageResult;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.fire.core.entity.WebsocketInfo;
import com.redxun.fire.core.pojo.base.ResultMsg;
import com.redxun.fire.core.pojo.base.TablePageData;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * 单店监控台弹窗接口
 */
public interface IFirePageWindowService {

    TablePageData getFireWindowInfoList(Map param);

    ResultMsg setFireTypeInfo(HttpServletRequest request, Map<String, Object> map);

    void paoDianOutTimeApprove(Map<String, Object> map);

    /**
     * 等待填报弹窗
     *
     * @param request
     * @return
     */
    TablePageData getWaiteFillWindow(HttpServletRequest request);

    Map<String, Object> getFalsePositiveInfo(HttpServletRequest request);

    TablePageData getPumpExpInfo(HttpServletRequest request);

    TablePageData getWaterExpInfo(HttpServletRequest request);

    TablePageData getWaterExpHandleInfo(HttpServletRequest request);

    Map<String, Integer> getWaterStatistic(HttpServletRequest request);

    TablePageData getPumpExpHandleInfo(HttpServletRequest request);

    TablePageData getCabinetExpHandleInfo(HttpServletRequest request);


    Map<String, Integer> getPumpStatistic(HttpServletRequest request);

    Map<String, Integer>  getCabinetStatistic(HttpServletRequest request);

    ResultMsg updateExpHandleInfo(HttpServletRequest request, Map<String, Object> map) throws ParseException;

    /**
     * 获取误报统计信息
     *
     * @param request
     * @return
     */
    List<Map<String, Object>> getWBStatistic(HttpServletRequest request);

    /**
     * 获取故障统计信息
     *
     * @param request
     * @return
     */
    List<Map<String, Object>> getFaultStatistic(HttpServletRequest request);

    ResultMsg afterApprover(HttpServletRequest request);

    ResultMsg getPaoDianName(HttpServletRequest request);

//    /**
//     * 照片上传
//     *
//     * @param uploadFile
//     * @return
//     */
//    String uploadPic(MultipartFile uploadFile) throws IOException;

    /**
     * 误报火警提交
     *
     * @param
     */
    void falsePositiveReported(HttpServletRequest request, Map<String, Object> param);

    /**
     * 测试火警提交
     *
     * @param
     */
    void testFireReported(HttpServletRequest request, Map<String, Object> param);

    List<WebsocketInfo> getFloorPlanExpection(HttpServletRequest request);

    /**
     * 火警处理填报统计
     *
     * @param request
     * @return
     */
    Map<String, Integer> getFireStatisticInfo(HttpServletRequest request);

    TablePageData getOutTimeInfo(HttpServletRequest request);

    TablePageData getFloorExpectionInfo(HttpServletRequest request);

    /**
     * 预警待处理查询弹窗
     *
     * @param request
     * @return
     */
    TablePageData getWaringInfoWindow(HttpServletRequest request);

    /**
     * 获取测试单位名单
     *
     * @param request
     * @return
     */
    List<Map<String, String>> getTestUnitName(HttpServletRequest request);

    /**
     * 预警待处数据条数统计
     *
     * @param request
     * @return
     */
    Map<String, Integer> getWaringStatisticInfo(HttpServletRequest request);

    /**
     * 预警处理超时查询
     *
     * @param request
     * @return
     */
    TablePageData getWaringOutTimeInfo(HttpServletRequest request);

    /**
     * 故障待处理/处理中/处理完成弹窗
     *
     * @param request
     * @return
     */
    TablePageData getFaultInfoWindow(HttpServletRequest request);

    /**
     * 故障处理超时弹窗
     *
     * @param request
     * @return
     */
    TablePageData getFaultOutTimeWindow(HttpServletRequest request);

    /**
     * 故障统计条数信息
     *
     * @param request
     * @return
     */
    Map<String, Integer> getFaultStatisticInfo(HttpServletRequest request);

    /**
     * 指派他人
     *
     * @param param
     * @return
     */
    ResultMsg appointOthers(Map<String, Object> param, HttpServletRequest request);

    /**
     * 获取维保人员信息
     *
     * @param request
     * @return
     */
    List<Map<String, Object>> getPersonnelInfo(HttpServletRequest request);

    /**
     * 获取当前建筑物维保单位名称
     *
     * @param request
     * @return
     */
    List<Map<String, Object>> getWbTeamInfo(HttpServletRequest request);

    /*
        故障忽略
     */
    ResultMsg faultOverLook(HttpServletRequest request) throws ParseException;

    /**
     * 预警完成处理
     *
     * @param request
     * @return
     */
    ResultMsg warningFigureOut(HttpServletRequest request);

    /**
     * 火警误报统计折线图导出
     *
     * @param: request
     * @return:
     */
    void exportWBStatistic(HttpServletRequest request, HttpServletResponse response) throws IOException;

    /**
     * 故障统计折线图导出
     *
     * @param: request, response
     * @return: void
     */
    void exportFaultStatistic(HttpServletRequest request, HttpServletResponse response) throws IOException;

    /**
     * 水压异常信息列表导出
     *
     * @param: request
     * @return: void
     */
    void exportWaterExpection(HttpServletRequest request, HttpServletResponse response) throws IOException;

    /**
     * 1.9.1.5水泵异常信息列表导出
     *
     * @param request response
     * @return
     */
    void exportPumpExpInfo(HttpServletRequest request, HttpServletResponse response) throws IOException;


    /**
     * app首页信息统计
     *
     * @param request
     * @return
     */
    Map<String, Integer> appFistPageStatistic(HttpServletRequest request);

    /**
     * app首页水压水泵火警预警建筑列表
     *
     * @param: request
     * @return: map
     */
    ResultMsg appFistPageBuildingList(HttpServletRequest request, Map<String, Object> param);

    /**
     * app首页水压水泵火警预警建筑列表
     *
     * @param: request
     * @return: map
     */
    ResultMsg appFistPageBuildingListCloseStore(HttpServletRequest request, Map<String, Object> param);

    boolean getBuildStatus(String buildId);

    ResultMsg getSingleInfo(String fireId);

    ResultMsg getFireHistory(HttpServletRequest request);

    List<String> getPeriotDate(int i);


    ResultMsg updateCabinetExpectionHandle(HttpServletRequest request, Map<String, Object> map);

    ResultMsg updateFireFocusInfo(HttpServletRequest request, Map<String, Object> map);

    List<WebsocketInfo> getFloorFireExpection(HttpServletRequest request);

    JsonPageResult getRealFirePage(QueryData queryData);
    JsonResult getLatestFireRecord(String wztUserId);

    Map<String, String> getFireDoorStatistic(HttpServletRequest request);

    /**
     * 获取各种异常数量统计
     * @param request HTTP请求，包含buildingId（必填）和devType（可选）参数
     * @return 按设备类型分组的异常统计结果
     */
    Map<String, Object> getExceptionStatistic(HttpServletRequest request);
}
