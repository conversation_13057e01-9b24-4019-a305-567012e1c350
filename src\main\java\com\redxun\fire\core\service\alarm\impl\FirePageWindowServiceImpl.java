package com.redxun.fire.core.service.alarm.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.redxun.api.feign.OrgPosClient;
import com.redxun.common.base.entity.JsonPageResult;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.common.base.search.QueryFilter;
import com.redxun.common.base.search.QueryFilterBuilder;
import com.redxun.common.tool.IdGenerator;
import com.redxun.common.tool.StringUtils;
import com.redxun.feign.bpm.BpmClient;
import com.redxun.feign.bpm.entity.BpmInst;
import com.redxun.fire.core.cassandra.dao.PreDataDao;
import com.redxun.fire.core.cassandra.entity.PumpData;
import com.redxun.fire.core.consts.BPMConstants;
import com.redxun.fire.core.consts.ConstantUtil;
import com.redxun.fire.core.consts.UserRoleConstants;
import com.redxun.fire.core.entity.*;
import com.redxun.fire.core.entity.wzt.UsersResponse;
import com.redxun.fire.core.entity.zhongan.StatFireDoorExpection;
import com.redxun.fire.core.feign.org.OrgClient;
import com.redxun.fire.core.influxdb.dao.InfluxDataDao;
import com.redxun.fire.core.mapper.*;
import com.redxun.fire.core.pojo.base.PageParam;
import com.redxun.fire.core.pojo.base.ResultMsg;
import com.redxun.fire.core.pojo.base.TablePageData;
import com.redxun.fire.core.pojo.dto.CassandraQueryDTO;
import com.redxun.fire.core.pojo.dto.ExceptionCountDto;
import com.redxun.fire.core.pojo.dto.app.AppBuildingDTO;
import com.redxun.fire.core.service.alarm.*;
import com.redxun.fire.core.service.bpm.IBpmForwardService;
import com.redxun.fire.core.service.monitor.IMonitorConsoleService;
import com.redxun.fire.core.service.other.ICheckService;
import com.redxun.fire.core.service.other.ITimeTaskService;
import com.redxun.fire.core.service.user.IOsUserService;
import com.redxun.fire.core.service.user.IUserDataPowerService;
import com.redxun.fire.core.service.user.impl.OrgMiddleServiceImpl;
import com.redxun.fire.core.service.zhongan.ElectricalFireMonitoringService;
import com.redxun.fire.core.service.zhongan.GasAlarmDeviceService;
import com.redxun.fire.core.utils.*;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FirePageWindowServiceImpl implements IFirePageWindowService {

    @Autowired
    private IBaseEventStatisticsService iBaseEventStatisticsService;
    @Resource
    private FirePageWindowMapper firePageWindowMapper;

    @Resource
    private FireInfoMapper fireInfoMapper;

    @Resource
    private IRepeatedlyMisinformationService repeatedlyMisinformationService;

    @Resource
    private StatWaterExpectionMapper statWaterExpectionMapper;

    @Resource
    private StatPumpExpectionMapper statPumpExpectionMapper;

    @Resource
    private BaseDevicePointMapper baseDevicePointMapper;

    @Resource
    private FaultInfoMapper faultInfoMapper;

    @Autowired
    private IWaterAbnormalService iWaterAbnormalService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private BpmClient bpmClient;

    @Autowired
    private ICheckService iCheckService;

    @Resource
    private ConfigRelationMapper configRelationMapper;

    @Resource
    private WaterMonitorService waterMonitorService;

    @Resource
    private BaseBuildingMapper baseBuildingMapper;

    @Resource
    private OrgClient orgClient;

    @Resource
    private IMonitorConsoleService monitorConsoleService;

//    @Resource
//    private FileServiceImpl fileService;

    @Resource
    private FaultWarningTransferLogMapper faultWarningTransferLogMapper;

    @Resource
    private ITimeTaskService timeTaskService;

    @Resource
    private StoreDbMapper storeDbMapper;

    @Resource
    private WaterAbnormalMapper waterAbnormalMapper;

//    @Autowired
//    private OsUserClient osUserClient;

    @Resource
    private FireAlarmMapper fireAlarmMapper;

    @Resource
    private ReceivingPointMapper receivingPointMapper;

    @Resource
    private FaultFireStatisticsMapper faultFireStatisticsMapper;

    @Resource
    private MonitorConsoleMapper monitorConsoleMapper;

    @Resource
    private RepeatedlyMisinformationMapper repeatedlyMisinformationMapper;

    @Resource
    private PreDataDao preDataDao;

    @Resource
    HttpClientUtil httpClientUtil;

    @Resource
    IBpmForwardService bpmForwardService;

    @Resource
    private StatCloseStoreExpectionMapper statCloseStoreExpectionMapper;

    @Resource
    private FireDoorExceptionMapper fireDoorExceptionMapper;

    @Resource
    private IUserDataPowerService userDataPowerService;

    @Autowired
    private IOsUserService osUserService;

    @Resource
    private PressureLinkPumpInfoMapper pressureLinkPumpInfoMapper;
    @Resource
    private OrgMiddleServiceImpl orgMiddleService;


    @Autowired
    private InfluxDataDao influxDataDao;

//    @Resource
//    private WaiXiaoService waiXiaoService;
    @Autowired
    private RealFireFocusInfoMapper realFireFocusInfoMapper;

    @Resource
    private OrgPosClient orgPosClient;
    @Autowired
    private StatCabinetExpectionMapper statCabinetExpectionMapper;

    //@Override
    /*public TablePageData getFireWindowInfoList(Map param) {
        //根据状态等信息查询火警
        List<Map<String, Object>> resultList = new ArrayList<>();
        Page<?> page = PageParam.initPagination(Integer.parseInt(param.get("pageIndex") + ""), Integer.parseInt(param.get("pageRows") + ""));
        //查询火警信息
        param.put("type", "0");
        //参数添加起止时间
        paramAddDate(param);
        //List<Map<String, Object>> fireInfoList = firePageWindowMapper.getWindowInfoListByPage(page, param);
        List<Map<String, Object>> fireInfoList = firePageWindowMapper.getWindowInfoBeforeListByPage(page, param);
        if (fireInfoList == null) {
            return null;
        }
        BaseBuilding baseBuilding = baseBuildingMapper.selectById(param.get("buildingId").toString());
        for (Map<String, Object> map : fireInfoList) {
            //查询7天内上报次数
            *//*int num = fireInfoMapper.selectCount(new QueryWrapper<FireInfo>().eq("building_id",param.get("buildingId")).eq("point_id",map.get("pointId"))
                    .ge("first_time",getPastDate(7,"yyyy-MM-dd HH:mm:ss")).le("first_time",DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss")));*//*
     *//*int num = 0;
            FarEastoneCache farEastoneCache = FastJSONUtils.toBean(redisUtils.get("point_status:"+map.get("buildingId") + "-" + map.get("pointId")) +"", FarEastoneCache.class);
            if(farEastoneCache!=null){
                num = farEastoneCache.getFireReportTimes();
            }*//*
            //map.put("num", map.get("num").toString());
            map.put("buildingName", baseBuilding.getBuildingName());
            map.put("ctrlPhone", baseBuilding.getCtrlPhone());
            map.put("centerPhone", baseBuilding.getCenterPhone());
            Map<String, Object> fireCheckInfo = getFireCheckInfo(String.valueOf(map.get("id")));
            map.putAll(fireCheckInfo);
            //获取最后一次上报时间 弱于当前时间相差超过五分钟 则超时报备按钮置灰
            String lastTime = String.valueOf(map.get("lastTime"));
            String isBaobei = "1";
            try {
                int i = DateUtils.minusMinute(new Date(), DateUtils.parseDate(lastTime));
                if (i > 5) {//超时 置灰
                    isBaobei = "0";
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }
            map.put("isBaobei", isBaobei);//0置灰 1正常
            resultList.add(map);
        }
        TablePageData result = new TablePageData();
        //获取完整分页信息
        PageParam pageParam = PageParam.getPage(Integer.parseInt(param.get("pageIndex") + ""), Integer.parseInt(param.get("pageRows") + ""));
        pageParam.setTotalRows(page.getTotal());
        result.setPageParam(pageParam);
        result.setTableData(resultList);
        return result;
    }*/

    @Override
    public TablePageData getFireWindowInfoList(Map param) {
        //根据状态等信息查询火警
        List<Map<String, Object>> resultList = new ArrayList<>();
        Page<?> page = PageParam.initPagination(Integer.parseInt(param.get("pageIndex") + ""), Integer.parseInt(param.get("pageRows") + ""));
        //查询火警信息
        param.put("type", "0");
        //参数添加起止时间
        paramAddDate(param);
        //List<Map<String, Object>> fireInfoList = firePageWindowMapper.getWindowInfoListByPage(page, param);
        List<Map<String, Object>> fireInfoList = firePageWindowMapper.getWindowInfoBeforeListByPage(page, param);
        if (CollectionUtils.isEmpty(fireInfoList)) {
            return null;
        }
        BaseBuilding baseBuilding = baseBuildingMapper.selectById(param.get("buildingId").toString());

        List<String> fireIds = new ArrayList<>();
        fireInfoList.forEach(map -> fireIds.add(String.valueOf(map.get("id"))));

        List<Map<String, Object>> fireCheckInfos = firePageWindowMapper.getFireCheckInfoByIds(fireIds);

        Map<String, List<Map<String, Object>>> fireCheckInfosMap = new HashMap<>();

        for (Map<String, Object> fireCheckInfoMap : fireCheckInfos) {
            String fire_id = String.valueOf(fireCheckInfoMap.get("fire_id"));
            List<Map<String, Object>> maps = fireCheckInfosMap.get(fire_id);
            if (CollectionUtils.isEmpty(maps)) {
                List<Map<String, Object>> list = new ArrayList<>();
                list.add(fireCheckInfoMap);
                fireCheckInfosMap.put(fire_id, list);
            } else {
                maps.add(fireCheckInfoMap);
            }
        }

        for (Map<String, Object> map : fireInfoList) {
            map.put("buildingName", baseBuilding.getBuildingName());
            map.put("ctrlPhone", baseBuilding.getCtrlPhone());
            map.put("centerPhone", baseBuilding.getCenterPhone());

            Map<String, Object> fireCheckInfo = getFireCheckInfoByMap(String.valueOf(map.get("id")), fireCheckInfosMap);
            map.putAll(fireCheckInfo);
            //获取最后一次上报时间 弱于当前时间相差超过五分钟 则超时报备按钮置灰
            String lastTime = String.valueOf(map.get("lastTime"));
            String isBaobei = "1";
            try {
                int i = DateUtils.minusMinute(new Date(), DateUtils.parseDate(lastTime));
                if (i > 5) {//超时 置灰
                    isBaobei = "0";
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }
            map.put("isBaobei", isBaobei);//0置灰 1正常
            resultList.add(map);
        }
        TablePageData result = new TablePageData();
        //获取完整分页信息
        PageParam pageParam = PageParam.getPage(Integer.parseInt(param.get("pageIndex") + ""), Integer.parseInt(param.get("pageRows") + ""));
        pageParam.setTotalRows(page.getTotal());
        result.setPageParam(pageParam);
        result.setTableData(resultList);
        return result;
    }

    private Map<String, Object> getFireCheckInfoByMap(String id, Map<String, List<Map<String, Object>>> fireCheckInfosMap) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> fireCheckInfo = fireCheckInfosMap.get(id);
        if (fireCheckInfo == null || fireCheckInfo.size() == 0) {
            resultMap.put("checkUserId", "");
            resultMap.put("checkUser", "");
            resultMap.put("fireType", "");
            resultMap.put("picList", "");
            return resultMap;
        }
        //图片集合
        List<Map<String, Object>> picList = new ArrayList<>();
        for (Map<String, Object> fireCheckInfoMap : fireCheckInfo) {
            Map<String, Object> map = new HashMap<>();
            map.put("path", fireCheckInfoMap.get("path").toString().replace("[", "").replace("]", ""));
            map.put("picId", fireCheckInfoMap.get("picId"));
            map.put("realName", fireCheckInfoMap.get("realName"));
            map.put("checkType", fireCheckInfoMap.get("checkType"));
            picList.add(map);
        }
        resultMap.put("checkTime", fireCheckInfo.get(0).get("checkTime"));
        resultMap.put("checkUserId", fireCheckInfo.get(0).get("checkUserId"));
        resultMap.put("checkUser", fireCheckInfo.get(0).get("checkUser"));
        resultMap.put("fireType", fireCheckInfo.get(0).get("fireType"));
        resultMap.put("picList", picList);
        return resultMap;
    }

    /**
     * 获取检查信息
     *
     * @param fireId
     * @return
     */
    public Map<String, Object> getFireCheckInfo(String fireId) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> fireCheckInfo = firePageWindowMapper.getFireCheckInfoList(fireId);
        if (fireCheckInfo == null || fireCheckInfo.size() == 0) {
            resultMap.put("checkUserId", "");
            resultMap.put("checkUser", "");
            resultMap.put("fireType", "");
            resultMap.put("picList", "");
            return resultMap;
        }
        //图片集合
        List<Map<String, Object>> picList = new ArrayList<>();
        for (Map<String, Object> fireCheckInfoMap : fireCheckInfo) {
            Map<String, Object> map = new HashMap<>();
            map.put("path", fireCheckInfoMap.get("path"));
            map.put("picId", fireCheckInfoMap.get("picId"));
            map.put("realName", fireCheckInfoMap.get("realName"));
            map.put("checkType", fireCheckInfoMap.get("checkType"));
            picList.add(map);
        }
        resultMap.put("checkTime", fireCheckInfo.get(0).get("checkTime"));
        resultMap.put("checkUserId", fireCheckInfo.get(0).get("checkUserId"));
        resultMap.put("checkUser", fireCheckInfo.get(0).get("checkUser"));
        resultMap.put("fireType", fireCheckInfo.get(0).get("fireType"));
        resultMap.put("picList", picList);
        return resultMap;
    }

    @Override
    public void paoDianOutTimeApprove(Map<String, Object> param) {
        String fireId = (String) param.get("fireId");
        String reportMan = (String) param.get("reportMan");
        String reasion = (String) param.get("reasion");

        FireInfo fireInfo = fireInfoMapper.selectInfoById(fireId);

        Map<String, Object> approve = new HashMap<>();
        Map<String, OverTimeReport> overReport = new HashMap<>();
        OverTimeReport overTimeReport = new OverTimeReport();
        overTimeReport.setBuilding_id(fireInfo.getBuildingId());
        overTimeReport.setBuilding_name(fireInfo.getBuildingName());
        overTimeReport.setOver_reason(reasion);
        overTimeReport.setPoint_code(fireInfo.getPointCode());
        overTimeReport.setPoint_id(fireInfo.getPointId());
        overTimeReport.setPoint_name(fireInfo.getDevName());
        overTimeReport.setReport_id(fireId);
        overTimeReport.setRun_man(reportMan);
        overTimeReport.setReport_time(fireInfo.getLastTime());
        overReport.put("over_time_report", overTimeReport);
        approve.put("checkType", "AGREE");
        approve.put("defKey", BPMConstants.OVERTIME_APPLY_ID);
        approve.put("formJson", JSONObject.toJSONString(overReport));

        String lastTime = fireInfo.getLastTime();
        try {
            int i = DateUtils.minusMinute(new Date(), DateUtils.parseDate(lastTime));
            if (i > 5) {//超时
                return;
            }
        } catch (ParseException e) {
            e.printStackTrace();
            return;
        }

        bpmClient.startProcess(JSONObject.parseObject(JSONObject.toJSONString(approve)));
    }

    /**
     * 设置火警信息类型
     *
     * @param param
     */
    @Override
    public ResultMsg setFireTypeInfo(HttpServletRequest request, Map<String, Object> param) {
        //Map param = ServletsUtil.getParameters(request);
        String fireType = String.valueOf(param.get("fireType"));
        String fireId = String.valueOf(param.get("id"));
        List<String> originList = getIdList(fireId);
        //如果是1条判断有没有处理过
        if (originList != null && originList.size() == 1) {
            FireInfo fireInfo = fireInfoMapper.selectById(originList.get(0));
            if ("1".equals(fireInfo.getFireStatus())) {
                return ResultMsg.getResultMsg("此火警已处理", 1, 200);
            }
        }

        List<String> idList = getSyncIds(originList);
        Integer syncCount = 0;
        for (String id : idList) {
            syncCount += handleFireInfo(request, id, fireType, param);
        }
//        try {
//            if(fireId.contains(",")){
//                //批量处理火警
//                String[] fireIds = fireId.split(",");
//                for (String id : fireIds) {
//                    handleFireInfo(id,fireType,param);
//                }
//            }else{
//                handleFireInfo(fireId,fireType,param);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        return ResultMsg.getResultMsg("处理成功", syncCount, 200);
    }


    private List<String> getSyncIds(List<String> idList) {
        List<FireInfo> fireInfos = fireInfoMapper.selectBatchIds(idList);
        ArrayList<String> result = new ArrayList<>();
        //同回路下所有点位id
        for (FireInfo fireInfo : fireInfos) {
            //筛选满足两分钟,超过十条的 pointId
            String lastTime = fireInfo.getLastTime();
            String pointId = fireInfo.getPointId();
            String timeSection = getDatebyTime(0.0, 0.0, 2.0, lastTime, "0", "yyyy-MM-dd HH:mm:ss");
            //去重
            Set<String> fireInfoIds = new HashSet<>(fireAlarmMapper.getSyncIds(pointId, lastTime, timeSection));
            if (fireInfoIds.size() >= 10) {
                result.addAll(fireInfoIds);
            } else {
                result.add(fireInfo.getId());
            }
        }
        return result;
    }

    /**
     * 字符串id 切割获取列表
     *
     * @param id
     * @return
     */
    private List<String> getIdList(String id) {
        List<String> idList = new ArrayList<>();
        if (id.contains(",")) {
            String[] split = id.split(",");
            Collections.addAll(idList, split);
        } else {
            //单个id 批量
            idList.add(id);
        }
        return idList;
    }


    public Integer handleFireInfo(HttpServletRequest request, String fireId, String fireType, Map<String, Object> param) {
        FireInfo fireInfo = fireInfoMapper.selectInfoById(fireId);
        //FireInfo fireInfo = fireInfoMapper.selectById(fireId);
        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        String userId = request.getHeader("xfUserId");//消防userId
        UsersResponse.DataDTO dataDto = orgMiddleService.findUserByUserId(wztUserId);
        if ("1".equals(fireInfo.getFireStatus())) {
            return 0;
        }

        //更新redis状态
        FarEastoneCache farEastoneCache = FastJSONUtils.toBean(redisUtils.get("point_status:" + fireInfo.getBuildingId() + "-" + fireInfo.getPointId()) + "", FarEastoneCache.class);
        if (farEastoneCache != null) {
            farEastoneCache.setFireHandleStatus("1");
            redisUtils.set("point_status:" + fireInfo.getBuildingId() + "-" + fireInfo.getPointId(), JSON.toJSONString(farEastoneCache));
        }

        fireInfo.setFireType(fireType);
        List<ReceivingPoint> receivingPoints = receivingPointMapper.selectList(new QueryWrapper<ReceivingPoint>().eq("event_id", fireId));
        BaseDevicePoint baseDevicePoint = baseDevicePointMapper.selectById(fireInfo.getPointId());
        if ("0".equals(fireInfo.getFireType())) {
            fireInfo.setFireTypeStr("误报火警");
            fireInfo.setExecuteResult("误报火警");
        } else if ("1".equals(fireInfo.getFireType())) {
            fireInfo.setFireTypeStr("测试火警");
            fireInfo.setExecuteResult("测试火警");
        } else if ("2".equals(fireInfo.getFireType())) {
            fireInfo.setFireTypeStr("确认火警");
            fireInfo.setExecuteResult("确认火警");
        } else if ("3".equals(fireInfo.getFireType())) {
            //下发消音命令
            //设备消音命令下发
            redisUtils.set("mute:" + baseDevicePoint.getDid(), "1");
            return 1;

        }
        if (receivingPoints != null && receivingPoints.size() > 0) {
            receivingPoints.get(0).setEventTypeStr(fireInfo.getFireTypeStr());
            receivingPoints.get(0).setProcessResultStr("已处理");
            receivingPointMapper.updateById(receivingPoints.get(0));
        }

        //更新处理，填报状态
        fireInfo.setFireStatus("1");
        fireInfo.setReportSituation("未填报");
        fireInfo.setReportStatus("0");
        fireInfo.setExecutorId(userId);
        fireInfo.setExecutor(dataDto.getFullName());
        fireInfo.setExecuteTime(DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
        /*if (fireInfo.getFeedbackTime() == null || "".equals(fireInfo.getFeedbackTime())) {
            fireInfo.setFeedbackTime(LocalDateTime.now());
        }*/

        if (param.get("reasion") != null) {
            firePageWindowMapper.updateFireCheck(param);
        }
        //误报消息推送误报信息表
        if (StringUtils.equals(fireType, "0")) {
            List<RepeatedlyMisinformation> rm = repeatedlyMisinformationMapper.selectList(new QueryWrapper<RepeatedlyMisinformation>().eq("fire_id", fireId));
            if (rm == null || rm.size() == 0) {
                RepeatedlyMisinformation repeatedlyMisinformation = new RepeatedlyMisinformation();
                repeatedlyMisinformation.setId(UUIDUtils.getUUID());
                repeatedlyMisinformation.setBuildingId(fireInfo.getBuildingId());
                repeatedlyMisinformation.setBuildingName(fireInfo.getBuildingName());
                repeatedlyMisinformation.setPointId(fireInfo.getPointId());
                repeatedlyMisinformation.setPointCode(fireInfo.getPointCode());
                repeatedlyMisinformation.setPointDesc(fireInfo.getPointDesc());
                repeatedlyMisinformation.setDevTypeCode(fireInfo.getDevType());
                repeatedlyMisinformation.setDevTypeName(fireInfo.getDevName());
                repeatedlyMisinformation.setFireId(fireId);
                try {
                    repeatedlyMisinformation.setReportTime(new Timestamp(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(fireInfo.getLastTime()).getTime()));
                } catch (Exception e) {
                    e.printStackTrace();
                }
                int count = repeatedlyMisinformationService.count(new LambdaQueryWrapper<RepeatedlyMisinformation>()
                        .eq(RepeatedlyMisinformation::getBuildingId, fireInfo.getBuildingId())
                        .eq(RepeatedlyMisinformation::getFireId, fireId)
                        .eq(RepeatedlyMisinformation::getPointId, fireInfo.getPointId()));
                if (count <= 0) {
                    repeatedlyMisinformationService.save(repeatedlyMisinformation);
                }
            }
            //插入反复误报点位数
            /*MisReportes misReportes = new MisReportes();
            misReportes.setReportEndTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            misReportes.setBuildingId(fireInfo.getBuildingId());
            misReportes.setDevTypeCode(baseDevicePoint.getDevTypeCode());
            //14天所有反复误报的点位id
            List<MisReportes> day14MisReportesList = misReportesMapper.getFourTeenDayMisRepPointCode(misReportes);
            //此乃7天内反复误报超过3次的所有点位id
            List<MisReportes> sevenDayMisReportesList = misReportesMapper.getSevenDayMisRepPointCode(misReportes);
            //此乃14天误报点位的每日误报次数
            MisReportesVo sqlParam = new MisReportesVo();
            sqlParam.setBuildingId(misReportes.getBuildingId());
            sqlParam.setDevTypeCode(misReportes.getDevTypeCode());
            sqlParam.setReportEndTime(misReportes.getReportEndTime());
            sqlParam.setPointList(day14MisReportesList);
            int wrongNumber7 = 0;
            int fourteenDayCount = 0;
            if(day14MisReportesList!=null && day14MisReportesList.size()>0){
                List<MisReportes> dayMisReportesList = misReportesMapper.getDayMisReports(sqlParam);
                fourteenDayCount = misReportesServiceImpl.getFounteenCount(fireInfo.getPointId(), dayMisReportesList, 2);

                for (MisReportes reportes : sevenDayMisReportesList) {
                    if(reportes.getPointId().equals(fireInfo.getPointId())){
                        wrongNumber7 = reportes.getSevenDayCount();
                        break;
                    }
                }
            }*/
            String endTime = fireInfo.getLastTime();
            System.out.println("误报次数结束时间：" + endTime);
            String fourteenTime = getDatebyTime(13.0, 0.0, 0.0, fireInfo.getLastTime(), "0", "yyyy-MM-dd") + " 00:00:00";
            System.out.println("14d误报次数开始时间：" + fourteenTime);
            String sevenTime = getDatebyTime(6.0, 0.0, 0.0, fireInfo.getLastTime(), "0", "yyyy-MM-dd") + " 00:00:00";
            System.out.println("7d误报次数开始时间：" + sevenTime);
            //int wrongNumber7 = firePageWindowMapper.getWorngCount(fireInfo.getPointId(),sevenTime,endTime);
            int wrongNumber7 = fireInfoMapper.selectCount(new QueryWrapper<FireInfo>().eq("point_id", fireInfo.getPointId())
                    .eq("type", '0').ge("last_time", sevenTime).le("last_time", endTime).ne("building_status_str", "维保"));
            System.out.println("7d误报次数：" + wrongNumber7);
            int fourteenDayCount = fireInfoMapper.selectCount(new QueryWrapper<FireInfo>().eq("point_id", fireInfo.getPointId()).eq("type", '0').ge("last_time", fourteenTime).le("last_time", endTime));
            System.out.println("14d误报次数：" + fourteenDayCount);
            /*if(wrongNumber7 == 0){
                wrongNumber7 = 1;
            }
            if(fourteenDayCount == 0){
                fourteenDayCount = 1;
            }*/

            fireInfo.setWrongNumber14(fourteenDayCount);
            fireInfo.setWrongNumber7(wrongNumber7);
        }


        fireInfoMapper.updateById(fireInfo);


        BaseBuilding baseBuilding = baseBuildingMapper.selectById(fireInfo.getBuildingId());

        log.info("门店调试状态--->" + getBuildStatus(fireInfo.getBuildingId()));
        log.info("火警类型是否为确认火警--->" + StringUtils.equals(fireType, "2"));
        if (StringUtils.equals(fireType, "2") && getBuildStatus(fireInfo.getBuildingId())) {
            //确认火警推送接警中心
            log.info("确认火警推送接警中心");
            BaseEventStatistics baseEventStatistics = new BaseEventStatistics();
            baseEventStatistics.setBuildId(fireInfo.getBuildingId());
            baseEventStatistics.setBuildName(baseBuilding.getBuildingName());
            baseEventStatistics.setEventType("0");
            baseEventStatistics.setEventTypeStr("确认火警");
            baseEventStatistics.setEventId(fireInfo.getId());
            //baseEventStatistics.setReportTime(fireInfo.getFirstTime());
            baseEventStatistics.setReportTime(DateUtil.formatDatetime(new Date(), "yyyy-MM-dd HH:mm:ss"));
            baseEventStatistics.setPointCode(baseDevicePoint.getPointNumber());
            baseEventStatistics.setPointDesc(baseDevicePoint.getPointDesc());
            baseEventStatistics.setPointId(baseDevicePoint.getId());
            baseEventStatistics.setProcessResult(fireInfo.getExecuteResult());
            baseEventStatistics.setProcessResultStr(fireInfo.getExecuteResult());
            baseEventStatistics.setZone_id(fireInfo.getZoneId());
            baseEventStatistics.setZoneName(fireInfo.getZoneName());
            baseEventStatistics.setTel(baseBuilding.getCtrlPhone());
            baseEventStatistics.setType("0");
            baseEventStatistics.setJjType(baseBuilding.getFormat());
            baseEventStatistics.setPointType(baseDevicePoint.getDevTypeCode());
            baseEventStatistics.setPointTypeStr(baseDevicePoint.getDevTypeName());
            baseEventStatistics.setBelongDep(baseBuilding.getBelongDep());
            baseEventStatistics.setCreateDepId(baseBuilding.getBelongDep());
            log.info("要入接警中心的数据为--->" + JSON.toJSONString(baseEventStatistics));
            boolean tag = iBaseEventStatisticsService.save(baseEventStatistics);
            log.info("入库结果为--->" + tag);

            //确认火警短信通知
            List<Map<String, Object>> configRelations = configRelationMapper.selectByConfigCode("dxtz_0_txgn");
            //通知0关闭1开启
            String flg = (String) configRelations.get(0).get(ConfigRelationUtil.CON_STR_VAL);
            if (StringUtils.equals("1", flg) && getBuildStatus(fireInfo.getBuildingId())) {
                for (int i = 1; i < configRelations.size(); i++) {
                    timeTaskService.affirmFireShortNote(configRelations.get(i), fireId, baseBuilding);
                }
            }
        }

        //超时填报短信通知定时任务
        List<Map<String, Object>> configRelations = configRelationMapper.selectByConfigCode("dxtz_3_txgn");
        //通知0关闭1开启
        String flg = (String) configRelations.get(0).get(ConfigRelationUtil.CON_STR_VAL);
        if (StringUtils.equals("1", flg) && getBuildStatus(fireInfo.getBuildingId())) {
            for (int i = 1; i < configRelations.size(); i++) {
                timeTaskService.reportFireShortNote(configRelations.get(i), fireId, baseBuilding);
            }
        }
        return 1;
    }

    @Override
    public TablePageData getWaiteFillWindow(HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        //String buildingId = (String)param.get("buildingId");
        Page<?> page = PageParam.initPagination(Integer.parseInt(param.get("pageIndex") + ""), Integer.parseInt(param.get("pageRows") + ""));
        //参数添加起止时间
        paramAddDate(param);
        //List<Map<String, Object>> fireInfos = fireInfoMapper.selectWaitFillInfo(page, param);
        List<Map<String, Object>> fireInfos = fireInfoMapper.selectWaitFillInfoAll(page, param);
        List<Map<String, Object>> resultList = new ArrayList<>();
        if (fireInfos != null && fireInfos.size() > 0) {
            for (Map<String, Object> fireInfo : fireInfos) {
                // String firstTime = fireInfo.get("executeTime");
                String executeTime = (String) fireInfo.get("executeTime");
                int min = DateUtils.minusMinute(new Date(), DateUtil.parseDate(executeTime, "yyyy-MM-dd HH:mm:ss"));
                String colourType = "绿色";
                //TODO:查找配置文件中的填报超时时间
                if (min > 5 && min <= 9) {
                    colourType = "橙色";
                } else if (min > 9) {
                    colourType = "红色";
                }
                Map<String, Object> fireInfoMap = JSON.parseObject(JSON.toJSONString(fireInfo), Map.class);
                fireInfoMap.put("colourType", colourType);
                resultList.add(fireInfoMap);
            }
        }
        TablePageData result = new TablePageData();
        //获取完整分页信息
        PageParam pageParam = PageParam.getPage(Integer.parseInt(param.get("pageIndex") + ""), Integer.parseInt(param.get("pageRows") + ""));
        pageParam.setTotalRows(page.getTotal());
        result.setPageParam(pageParam);
        result.setTableData(resultList);
        return result;
    }

    @Override
    public Map<String, Object> getFalsePositiveInfo(HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        return getFireCheckInfo((String) param.get("id"));
    }


    /**
     * 获取现场处置用户集合
     *
     * @param request
     * @return
     */
    @Override
    public ResultMsg getPaoDianName(HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        String buildingId = (String) param.get("buildingId");
        if (StringUtils.isBlank(buildingId)) {
            return ResultMsg.getResultMsg("buildingId不存在", 10000);
        }
        BaseBuilding baseBuilding = baseBuildingMapper.selectById(buildingId);
        // 通过广场信息获取跑点用户信息(现场处置)
        List<OsUser> osUsers = osUserService.selectUserListByRoleKey(baseBuilding.getPiazza(), UserRoleConstants.SITE_DISPOSAL);
        List<Map<String, Object>> result = Lists.newArrayList();
        osUsers.forEach(osUser -> {
            Map<String, Object> map = Maps.newHashMap();
            map.put("userId", osUser.getUserId());
            map.put("fullName", osUser.getFullname());
            result.add(map);
        });
//        BaseBuilding baseBuilding = baseBuildingMapper.selectById(buildingId);
//        String belongDep = baseBuilding.getBelongDep();
//        if (StringUtils.isBlank(belongDep)) {
//            return ResultMsg.getResultMsg("完善建筑基础信息中的所属组织机构id", 10000);
//        }
        return ResultMsg.getResultMsg("获取名单成功", result, 200);
    }

    /**
     * 获取水泵列表信息
     *
     * @param request
     * @return
     */
    @Override
    public TablePageData getPumpExpInfo(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        Page<?> page = PageParam.initPagination(Integer.parseInt(param.get("pageIndex") + ""), Integer.parseInt(param.get("pageRows") + ""));
        //参数添加起止时间
        paramAddDate(param);
        List<Map<String, Object>> pumpExpectionList = firePageWindowMapper.getPumpExpectionListByPage(page, param);
        TablePageData result = new TablePageData();
        //获取完整分页信息
        PageParam pageParam = PageParam.getPage(Integer.parseInt(param.get("pageIndex") + ""), Integer.parseInt(param.get("pageRows") + ""));
        pageParam.setTotalRows(page.getTotal());
        result.setPageParam(pageParam);
        result.setTableData(pumpExpectionList);
        return result;
    }

    /**
     * 获取水压列表信息
     *
     * @param request
     * @return
     */
    @Override
    public TablePageData getWaterExpInfo(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        Page<?> page = PageParam.initPagination(Integer.parseInt(param.get("pageIndex") + ""), Integer.parseInt(param.get("pageRows") + ""));
        //参数添加起止时间
        paramAddDate(param);
        //List<Map<String, Object>> waterExpectionList = firePageWindowMapper.getWaterExpectionListByPage(page, param);
        List<Map<String, Object>> waterExpectionList = firePageWindowMapper.getWaterExpectionAllListByPage(page, param);
        //获取完整分页信息
        PageParam pageParam = PageParam.getPage(Integer.parseInt(param.get("pageIndex") + ""), Integer.parseInt(param.get("pageRows") + ""));
        TablePageData result = new TablePageData();
        pageParam.setTotalRows(page.getTotal());
        result.setPageParam(pageParam);
        result.setTableData(waterExpectionList);
        return result;
    }

    /**
     * 水压异常处理弹窗
     *
     * @param request
     * @return
     */
    @Override
    public TablePageData getWaterExpHandleInfo(HttpServletRequest request) {
        //List<Map<String,Object>> result = new ArrayList<>();
        Map<String, Object> param = ServletsUtil.getParameters(request);
        Page<?> page = PageParam.initPagination(Integer.parseInt(param.get("pageIndex") + ""), Integer.parseInt(param.get("pageRows") + ""));
        String type = null;
        String floorId = null;
        if (param.get("type") != null) {
            type = (String) param.get("type");

        }
        if (param.get("floorId") != null) {
            floorId = (String) param.get("floorId");
        }


        List<String> expectionType = new ArrayList<>();
        if (StringUtils.equals(type, "0")) {
            //水压异常
            expectionType.add("0");
            expectionType.add("1");
        } else if (StringUtils.equals(type, "1")) {
            //液位异常
            expectionType.add("2");
            expectionType.add("3");
        } else if (StringUtils.equals(type, "2")) {
            //设备离线
            expectionType.add("4");
            expectionType.add("5");
        } else {
            //分析异常
            expectionType.add("6");
        }
        param.put("expectionType", expectionType);
        //参数添加起止时间
        paramAddDate(param);
        param.put("floorId", floorId);
        //List<Map<String, Object>> fistList = firePageWindowMapper.getWaterExpectionHandleListByPage(page, param);
        List<Map<String, Object>> fistList = firePageWindowMapper.getWaterExpectionHandleAllListByPage(page, param);
        //result.addAll(fistList);
        //查询维持时间结束是当前时间范围内的
        //List<Map<String,Object>> twoList = firePageWindowMapper.getWaterPushHandleInfo(param);
        //result.addAll(twoList);
        //获取完整分页信息
        PageParam pageParam = PageParam.getPage(Integer.parseInt(param.get("pageIndex") + ""), Integer.parseInt(param.get("pageRows") + ""));
        TablePageData result = new TablePageData();
        pageParam.setTotalRows(page.getTotal());
        result.setPageParam(pageParam);
        result.setTableData(fistList);
        return result;
    }

    /**
     * 水压异常信息统计
     *
     * @param request
     * @return
     */
    @Override
    public Map<String, Integer> getWaterStatistic(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        //参数添加起止时间
        paramAddDate(param);
        int preError = 0;
        int waterLevelError = 0;
        int iotError = 0;
        int parsingError = 0;
        //List<Map<String, Object>> StatWaterExpections = firePageWindowMapper.selectWaterExpection(param);
        List<Map<String, Object>> StatWaterExpections = firePageWindowMapper.selectAllWaterExpection(param);
        for (Map<String, Object> statWaterExpection : StatWaterExpections) {
            String expectionType = (String) statWaterExpection.get("expectionType");
            if (StringUtils.equals(expectionType, "水压过高") || StringUtils.equals(expectionType, "水压过低")) {
                preError++;
            } else if (StringUtils.equals(expectionType, "液位过高") || StringUtils.equals(expectionType, "液位过低")) {
                waterLevelError++;
            } else if (StringUtils.equals(expectionType, "设备故障") || StringUtils.equals(expectionType, "设备离线")) {
                iotError++;
            } else if (StringUtils.equals(expectionType, "分析异常")) {
                parsingError++;
            }
        }

        Map<String, Integer> result = new HashMap<>();
        result.put("preError", preError);
        result.put("waterLevelError", waterLevelError);
        result.put("iotError", iotError);
        result.put("parsingError", parsingError);
        return result;
    }

    /**
     * 水泵异常处理弹窗
     *
     * @param request
     * @return
     */
    @Override
    public TablePageData getPumpExpHandleInfo(HttpServletRequest request) {
        //List<Map<String,Object>> result = new ArrayList<>();
        Map<String, Object> param = ServletsUtil.getParameters(request);
        Page<?> page = PageParam.initPagination(Integer.parseInt(param.get("pageIndex") + ""), Integer.parseInt(param.get("pageRows") + ""));
        //参数添加起止时间
        paramAddDate(param);
        //List<Map<String, Object>> fistList = firePageWindowMapper.getPumpExpectionHandleListByPage(page, param);
        List<Map<String, Object>> fistList = firePageWindowMapper.getAllPumpExpectionHandleListByPage(page, param);
        //result.addAll(fistList);
        //查询维持时间结束是当前时间范围内的
        //List<Map<String,Object>> twoList = firePageWindowMapper.getPumpPushHandleInfo(param);
        //result.addAll(twoList);
        PageParam pageParam = PageParam.getPage(Integer.parseInt(param.get("pageIndex") + ""), Integer.parseInt(param.get("pageRows") + ""));
        TablePageData result = new TablePageData();
        pageParam.setTotalRows(page.getTotal());
        result.setPageParam(pageParam);
        result.setTableData(fistList);
        return result;
    }


    @Override
    public TablePageData getCabinetExpHandleInfo(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        Page<?> page = PageParam.initPagination(Integer.parseInt(param.get("pageIndex") + ""), Integer.parseInt(param.get("pageRows") + ""));
        //参数添加起止时间
        paramAddDate(param);
        List<Map<String, Object>> fistList = firePageWindowMapper.getAllCabinetExpectionHandleListByPage(page, param);
        PageParam pageParam = PageParam.getPage(Integer.parseInt(param.get("pageIndex") + ""), Integer.parseInt(param.get("pageRows") + ""));
        TablePageData result = new TablePageData();
        pageParam.setTotalRows(page.getTotal());
        result.setPageParam(pageParam);
        result.setTableData(fistList);
        return result;
    }

    /**
     * 水泵异常信息统计
     *
     * @param request
     * @return
     */
    @Override
    public Map<String, Integer> getPumpStatistic(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        //参数添加起止时间
        paramAddDate(param);
        //停电
        int stop = 0;
        //手动
        int hand = 0;
        //运行
        int run = 0;
        //故障
        int error = 0;
        //离线
        int leave = 0;
        // 联锁
        int link = 0;
        //今日数据
        //List<Map<String, Object>> statPumpExpections = firePageWindowMapper.selectPumpExpection(param);
        //今日 + 今日之前
        List<Map<String, Object>> statPumpExpections = firePageWindowMapper.selectAllPumpsExpection(param);
        for (Map<String, Object> statPumpExpection : statPumpExpections) {
            String expectionType = (String) statPumpExpection.get("expectionType");
            if (StringUtils.equals(expectionType, "停电")) {
                stop++;
            } else if (StringUtils.equals(expectionType, "手动")) {
                hand++;
            } else if (StringUtils.equals(expectionType, "运行")) {
                run++;
            } else if (StringUtils.equals(expectionType, "设备故障")) {
                error++;
            } else if (StringUtils.equals(expectionType, "设备离线")) {
                leave++;
            } else if (StringUtils.equals(expectionType, "联锁异常")) {
                link++;
            }
        }

        Map<String, Integer> result = new HashMap<>();
        result.put("stop", stop);
        result.put("hand", hand);
        result.put("run", run);
        result.put("error", error);
        result.put("leave", leave);
        result.put("link", link);
        return result;
    }

    @Override
    public Map<String, Integer> getCabinetStatistic(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        //参数添加起止时间
        paramAddDate(param);
        //停电
        int stop = 0;
        //手动
        int hand = 0;
        //运行
        int run = 0;
        //故障
        int error = 0;
        //离线
        int leave = 0;
        // 联锁
        int link = 0;
        //今日数据
        //List<Map<String, Object>> statPumpExpections = firePageWindowMapper.selectPumpExpection(param);
        //今日 + 今日之前
        List<Map<String, Object>> statPumpExpections = firePageWindowMapper.selectAllCabinetExpection(param);
        for (Map<String, Object> statPumpExpection : statPumpExpections) {
            String expectionType = (String) statPumpExpection.get("expectionType");
            if (StringUtils.equals(expectionType, "停电")) {
                stop++;
            } else if (StringUtils.equals(expectionType, "手动")) {
                hand++;
            } else if (StringUtils.equals(expectionType, "运行")) {
                run++;
            } else if (StringUtils.equals(expectionType, "设备故障")) {
                error++;
            } else if (StringUtils.equals(expectionType, "设备离线")) {
                leave++;
            } else if (StringUtils.equals(expectionType, "联锁异常")) {
                link++;
            }
        }

        Map<String, Integer> result = new HashMap<>();
        result.put("stop", stop);
        result.put("hand", hand);
        result.put("run", run);
        result.put("error", error);
        result.put("leave", leave);
        result.put("link", link);
        return result;
    }

    /**
     * 异常处理(批量处理)
     *
     * @param
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public ResultMsg updateExpHandleInfo(HttpServletRequest request, Map<String, Object> param) throws ParseException {
        String module = String.valueOf(param.get("module"));
        String id = String.valueOf(param.get("id"));
        String method = String.valueOf(param.get("method"));
        ResultMsg rm = new ResultMsg();
        if (id.contains(",")) {
            String[] ids = id.split(",");
            for (String s : ids) {
                rm = handleExpHandleInfo(request, s, method, module, param);
                if (rm.getCode() == 10000) {
                    rm = ResultMsg.getResultMsg("处理成功,等待审批", 200);
                    continue;
                }
            }
        } else {
            rm = handleExpHandleInfo(request, id, method, module, param);
        }
        return rm;
    }

    //异常处理
    public ResultMsg handleExpHandleInfo(HttpServletRequest request, String id, String method, String module, Map<String, Object> param) throws ParseException {
        log.info("根据异常id获取水系统异常记录--->" + id);
        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        String userId = request.getHeader("xfUserId");//消防userId
        UsersResponse.DataDTO dataDto = orgMiddleService.findUserByUserId(wztUserId);
        QueryWrapper<WaterAbnormal> queryWrapper = new QueryWrapper<WaterAbnormal>();
        queryWrapper.eq("abnirmal_id", id);
//        log.info(JSON.toJSONString(iWaterAbnormalService.list(queryWrapper)));
        WaterAbnormal waterAbnormal = iWaterAbnormalService.getOne(queryWrapper);
        log.info("水系统异常记录数据--->" + JSON.toJSONString(waterAbnormal));
        if (waterAbnormal != null) {
            waterAbnormal.setProcessor(dataDto.getFullName());
            if (StringUtils.equals(method, "0")) {
                waterAbnormal.setProcessTime(LocalDateTime.now());
            }
        }
        if (StringUtils.equals(module, "0")) {
            StatWaterExpection statWaterExpection = statWaterExpectionMapper.selectById(id);
            //水压
            if (StringUtils.equals(method, "0")) {
                //判断实际是否已处理
                String pointId = statWaterExpection.getPointId();
                //String pointCode = statWaterExpection.getPointCode();
                String buildingId = statWaterExpection.getBuildingId();
                String expectionType = statWaterExpection.getExpectionType();
                String expectionStatus = statWaterExpection.getExpectionStatus();
                //String decType = statWaterExpection.getDevType();
                String today = DateUtil.formatDate(new Date(), "yyyy-MM-dd");
                //String endTime = DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
                BaseDevicePoint baseDevicePoint = baseDevicePointMapper.selectById(pointId);
                if (baseDevicePoint != null) {
                    //                //统计表
//                List<StatWaterPressureEveryday> StatWaterPressureEverydayList = firePageWindowMapper.selectByBuildingIdForUpdate(buildingId, today);
                    //获取设备类型代码
                    String superType = baseDevicePoint.getSuperType();
                    //查询该设备最小值

                    Double min = Double.valueOf(waterMonitorService.getMinValByDevTypeCode(baseDevicePoint.getDevTypeCode()));
                    Map<String, String> redisData = null;
                    if (StringUtils.equals(expectionType, "0") || StringUtils.equals(expectionType, "1")) {
                        redisData = (Map<String, String>) JSON.parse((String) redisUtils.get(buildingId + "_pressure_" + pointId));
                        if (null == redisData) {
                            return ResultMsg.getResultMsg("redis 没有数据", 10000);
                        }
                        Double value = Double.valueOf(redisData.get("value"));
                        //获取注册时最大值
                        //Double max = Double.valueOf(String.valueOf())/1000;
                        Double max = new BigDecimal(baseDevicePoint.getMaxval() + "").divide(new BigDecimal(1000)).doubleValue();
                        //水压过高，过低
                        if (!(value >= min && value <= max)) {
                            return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
                        }
                        if (redisData != null) {
                            redisData.put("waterHandleStatus", "1");
                            redisUtils.set(buildingId + "_pressure_" + pointId, JSONObject.toJSONString(redisData));
                        }

//                    //水压监测统计实时数据表减1
//                    //判断所有异常状态，如果是暂不处理的，不对统计数据减1
//                    if (StatWaterPressureEverydayList != null && StatWaterPressureEverydayList.size() > 0 && !StringUtils.equals(expectionStatus, "2")) {
//                        StatWaterPressureEverydayList.get(0).setPreAbnormalNum(StatWaterPressureEverydayList.get(0).getPreAbnormalNum() - 1);
//                        /*if(StringUtils.equals(expectionType,"1") && !StringUtils.equals(expectionStatus,"2")){
//                            //水压过低数减1
//                            StatWaterPressureEverydayList.get(0).setWaterDownPoint(StatWaterPressureEverydayList.get(0).getWaterDownPoint()-1);
//                        }else if(StringUtils.equals(expectionType,"0") && !StringUtils.equals(expectionStatus,"2")){
//                            //水压过高数减1
//
//                        }*/
//                    }
                    } else if (StringUtils.equals(expectionType, "2") || StringUtils.equals(expectionType, "3")) {
                        //液位过高，过低
                        redisData = (Map<String, String>) JSON.parse((String) redisUtils.get(buildingId + "_liquid_" + pointId));
                        Double value = Double.valueOf(redisData.get("value"));
                        //获取注册时最大值
                        Double max = Double.valueOf(String.valueOf(baseDevicePoint.getMaxval()));
                        if (!(value >= min && value <= max)) {
                            return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
                        }

//                    //判断所有异常状态，如果是暂不处理的，不对统计数据减1
//                    if (StatWaterPressureEverydayList != null && StatWaterPressureEverydayList.size() > 0 && !StringUtils.equals(expectionStatus, "2")) {
//                        StatWaterPressureEverydayList.get(0).setLiquidAbnormalNum(StatWaterPressureEverydayList.get(0).getLiquidAbnormalNum() - 1);
//                       /* if(StringUtils.equals(expectionType,"3") && !StringUtils.equals(expectionStatus,"2")){
//                            //液位过低数减1
//
//                        }else if(StringUtils.equals(expectionType,"2") && !StringUtils.equals(expectionStatus,"2")){
//                            StatWaterPressureEverydayList.get(0).setLiquidOnPoint(StatWaterPressureEverydayList.get(0).getLiquidOnPoint()-1);
//                        }*/
//                    }
                        if (redisData != null) {
                            redisData.put("waterHandleStatus", "1");
                            redisUtils.set(buildingId + "_liquid_" + pointId, JSONObject.toJSONString(redisData));
                        }
                    } else if (StringUtils.equals(expectionType, "4")) {
                        //设备故障
                        if (StringUtils.equals(superType, "2")) {
                            //压力表故障
                            redisData = (Map<String, String>) JSON.parse((String) redisUtils.get(buildingId + "_pressure_" + pointId));
                            if (!StringUtils.equals(redisData.get("faultStatus"), "0")) {//判断是否正常
                                return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
                            }
                            if (redisData != null) {
                                redisData.put("waterHandleStatus", "1");
                                redisUtils.set(buildingId + "_pressure_" + pointId, JSONObject.toJSONString(redisData));
                            }
                        } else if (StringUtils.equals(superType, "3")) {
                            //液位表故障
                            redisData = (Map<String, String>) JSON.parse((String) redisUtils.get(buildingId + "_liquid_" + pointId));
                            if (!StringUtils.equals(redisData.get("faultStatus"), "0")) {//判断是否正常
                                return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
                            }
                            if (redisData != null) {
                                redisData.put("waterHandleStatus", "1");
                                redisUtils.set(buildingId + "_liquid_" + pointId, JSONObject.toJSONString(redisData));
                            }
                        }
//                    if (StatWaterPressureEverydayList != null && StatWaterPressureEverydayList.size() > 0 && !StringUtils.equals(expectionStatus, "2")) {
//                        //故障数减1
//                        StatWaterPressureEverydayList.get(0).setDeviceFaultNum(StatWaterPressureEverydayList.get(0).getDeviceFaultNum() - 1);
//                    }
                    } else if (StringUtils.equals(expectionType, "5")) {
                        //设备离线
                        if (StringUtils.equals(superType, "2")) {
                            //压力表离线
                            redisData = (Map<String, String>) JSON.parse((String) redisUtils.get(buildingId + "_pressure_" + pointId));
                            log.info("------------------redisData得数据为{}-------------------------------", redisData == null ? "null" : JSONObject.toJSONString(redisData));
                            if (redisData != null && "1".equals(redisData.get("deviceStatus"))) {
                                return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
                            }
                            if (redisData != null) {
                                redisData.put("waterHandleStatus", "1");
                                redisUtils.set(buildingId + "_pressure_" + pointId, JSONObject.toJSONString(redisData));
                            }
//                        long lastHeartbeatTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(redisData.get("lastHeartbeatTime")).getTime();
//                        long nowTime = System.currentTimeMillis();
//                        //TODO:确定信息间隔时间
//                        if (nowTime - lastHeartbeatTime > 50000) {
//                            return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
//                        }
                        } else if (StringUtils.equals(superType, "3")) {
                            //液位表离线
                            redisData = (Map<String, String>) JSON.parse((String) redisUtils.get(buildingId + "_liquid_" + pointId));
                            if ("1".equals(redisData.get("deviceStatus"))) {
                                return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
                            }
                            if (redisData != null) {
                                redisData.put("waterHandleStatus", "1");
                                redisUtils.set(buildingId + "_liquid_" + pointId, JSONObject.toJSONString(redisData));
                            }
//                        long lastHeartbeatTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(redisData.get("lastHeartbeatTime")).getTime();
//                        long nowTime = System.currentTimeMillis();
//                        //TODO:确定信息间隔时间,暂定50秒一次
//                        if (nowTime - lastHeartbeatTime > 50000) {
//                            return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
//                        }
                        }
//                    if (StatWaterPressureEverydayList != null && StatWaterPressureEverydayList.size() > 0 && !StringUtils.equals(expectionStatus, "2")) {
//                        //故障数减1
//                        StatWaterPressureEverydayList.get(0).setDeviceOffNum(StatWaterPressureEverydayList.get(0).getDeviceOffNum() - 1);
//                    }
                    } else if (StringUtils.equals(expectionType, "6")) {
                        //分析异常
                        if (baseDevicePoint.getSuperType().equals(ConstantUtil.DEVICE_TYPE_PRESSURE_GAGE)) {
                            redisData = (Map<String, String>) JSON.parse((String) redisUtils.get(buildingId + ConstantUtil.REDIS_KEY_WATER_PRESSURE + pointId));
                        } else if (baseDevicePoint.getSuperType().equals(ConstantUtil.DEVICE_TYPE_LIQUID_GAGE)) {
                            redisData = (Map<String, String>) JSON.parse((String) redisUtils.get(buildingId + ConstantUtil.REDIS_KEY_WATER_LIQUID + pointId));
                        }
                        List<Map<String, Object>> message = configRelationMapper.selectByConfigCode("sybj_8sx");
                        Double hourSetting = Double.valueOf(message.get(0).get("config_double_data").toString());
                        if (redisData != null && redisData.get("lastTime") != null) {
                            int minute = DateUtils.minusMinute(new Date(), DateUtil.parseDate(redisData.get("lastTime").toString(), "yyyy-MM-dd HH:mm:ss"));
                            if (minute > (hourSetting * 60)) {
                                return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
                            }
                            redisData.put("analyseAbnormalStatus", "1");
                            redisData.put("waterHandleStatus", "1");
                            redisUtils.set(buildingId + "_pressure_" + pointId, JSONObject.toJSONString(redisData));
                        }

//                        if (!StringUtils.equals(analyseAbnormalStatus, "0")) {
//                            return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
//                        }
//                        if (redisData != null) {
//                            redisData.put("waterHandleStatus", "1");
//                            redisUtils.set(buildingId + "_pressure_" + pointId, JSONObject.toJSONString(redisData));
//                        }
//                    if (StatWaterPressureEverydayList != null && StatWaterPressureEverydayList.size() > 0 && !StringUtils.equals(expectionStatus, "2")) {
//                        //分析数减1
//                        StatWaterPressureEverydayList.get(0).setAnalyseAbnormalNum(StatWaterPressureEverydayList.get(0).getAnalyseAbnormalNum() - 1);
//                    }
                    }

                }
                //完成处理更新状态
                statWaterExpection.setExpectionStatus("1");
                statWaterExpection.setHandlingTime(DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
                statWaterExpection.setHandlingId(userId);
                statWaterExpection.setHandlingName(dataDto.getFullName());
                if (param.get("handlingInfo") != null) {
                    statWaterExpection.setHandlingInfo(String.valueOf(param.get("handlingInfo")));
                }
                statWaterExpectionMapper.updateById(statWaterExpection);

                log.info("修改水系统异常记录表   处理成功");
                if (waterAbnormal != null) {
                    waterAbnormal.setProcessResult("处理成功");
                    iWaterAbnormalService.updateById(waterAbnormal);
                }
                return ResultMsg.getResultMsg("处理成功", 200);
            } else {

                //调用审批流接口


                /*{
                    "checkType":"AGREE",
                        "defId":"1321036270847148034",
                        "formJson":"{\"yssqb\":{\"dwid\":\"1\",\"jzwmc\":\"1\",\"dwmc\":\"1\",\"yssj\":\"1\",\"jzwid\":\"1\",\"ycid\":\"1\",\"ysxs\":\"1\",\"sblx\":\"1\",\"ysclyy\":\"1\"}}"

                }*/
                //之前已经是暂不处理
                if (("2").equals(statWaterExpection.getExpectionStatus())
                        && DateUtil.parseDate(statWaterExpection.getEndTime(), "yyyy-MM-dd HH:mm:ss").getTime() >= System.currentTimeMillis()) {
                    return ResultMsg.getResultMsg("已提交暂不处理,等待审批中无需重复提交。", 500);
                }


                String expectionType = statWaterExpection.getExpectionType();
                String buildingId = statWaterExpection.getBuildingId();
                String expectionStatus = statWaterExpection.getExpectionStatus();
                String pointId = statWaterExpection.getPointId();
                //调用审批流程接口
                Double day = Double.valueOf(String.valueOf(param.get("day")));
                day = statWaterExpection.getDay() == null ? day : (Double.valueOf(statWaterExpection.getDay()) + day);
                Double hour = Double.valueOf(String.valueOf(param.get("hour")));
                hour = statWaterExpection.getHour() == null ? hour : (Double.valueOf(statWaterExpection.getHour()) + hour);
                if (hour > 24) {
                    day = day + (hour / 24);
                    hour = hour % 24;
                }
                //查水压基础时长配置表
                List<Map<String, Object>> configRelation = configRelationMapper.selectByConfigCode("splc_0_jcsc");
                //数据库天数存在小时字段里了
                Double dayTime = Double.valueOf(configRelation.get(0).get("config_int_day") + "");
                Double hourTime = Double.valueOf(configRelation.get(0).get(ConfigRelationUtil.CON_INT_HOUR) + "");
                if (day > dayTime || (day <= dayTime && hour > (dayTime * 24 + hourTime))) {
                    //超过基础时长
                    approve(request, "1326050225323769857", buildingId, pointId, id, param);
                } else {
                    //未超过
                    approve(request, "1325696464516575234", buildingId, pointId, id, param);
                }

                //更新水压统计表

                String today = DateUtil.formatDate(new Date(), "yyyy-MM-dd");
                /*List<StatWaterPressureEveryday> StatWaterPressureEverydayList = firePageWindowMapper.selectByBuildingIdForUpdate(buildingId, today);
                if (StringUtils.equals(expectionType, "0") || StringUtils.equals(expectionType, "1")) {
                    //水压监测统计实时数据表减1
                    //判断所有异常状态，如果是暂不处理的，不对统计数据减1
                    if (StatWaterPressureEverydayList != null && StatWaterPressureEverydayList.size() > 0 && !StringUtils.equals(expectionStatus, "2")) {
                        StatWaterPressureEverydayList.get(0).setPreAbnormalNum(StatWaterPressureEverydayList.get(0).getPreAbnormalNum() - 1);
                    }
                } else if (StringUtils.equals(expectionType, "2") || StringUtils.equals(expectionType, "3")) {

                    //判断所有异常状态，如果是暂不处理的，不对统计数据减1
                    if (StatWaterPressureEverydayList != null && StatWaterPressureEverydayList.size() > 0 && !StringUtils.equals(expectionStatus, "2")) {
                        StatWaterPressureEverydayList.get(0).setLiquidAbnormalNum(StatWaterPressureEverydayList.get(0).getLiquidAbnormalNum() - 1);
                    }
                } else if (StringUtils.equals(expectionType, "4")) {
                    if (StatWaterPressureEverydayList != null && StatWaterPressureEverydayList.size() > 0 && !StringUtils.equals(expectionStatus, "2")) {
                        //故障数减1
                        StatWaterPressureEverydayList.get(0).setDeviceFaultNum(StatWaterPressureEverydayList.get(0).getDeviceFaultNum() - 1);
                    }
                } else if (StringUtils.equals(expectionType, "5")) {
                    if (StatWaterPressureEverydayList != null && StatWaterPressureEverydayList.size() > 0 && !StringUtils.equals(expectionStatus, "2")) {
                        //故障数减1
                        StatWaterPressureEverydayList.get(0).setDeviceOffNum(StatWaterPressureEverydayList.get(0).getDeviceOffNum() - 1);
                    }
                } else if (StringUtils.equals(expectionType, "6")) {
                    if (StatWaterPressureEverydayList != null && StatWaterPressureEverydayList.size() > 0 && !StringUtils.equals(expectionStatus, "2")) {
                        //分析数减1
                        StatWaterPressureEverydayList.get(0).setAnalyseAbnormalNum(StatWaterPressureEverydayList.get(0).getAnalyseAbnormalNum() - 1);
                    }
                }
                //更新水压监测统计实时数据表
                statWaterPressureEverydayMapper.updateById(StatWaterPressureEverydayList.get(0));*/


                //暂不处理
                statWaterExpection.setExpectionStatus("2");
                statWaterExpection.setHandlingInfo(param.get("handlingInfo") == null ? "" : String.valueOf(param.get("handlingInfo")));
                statWaterExpection.setReason(param.get("reason") == null ? "" : String.valueOf(param.get("reason")));
                statWaterExpection.setHandlingTime(DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
                statWaterExpection.setHandlingId(userId);
                statWaterExpection.setHandlingName(dataDto.getFullName());
                if (day != null) {
                    statWaterExpection.setDay(String.valueOf(param.get("day")));
                }
                if (hour != null) {
                    statWaterExpection.setHour(String.valueOf(param.get("hour")));
                }

                String data = null;
                if (StringUtils.isNoneBlank(statWaterExpection.getEndTime())) {
                    //data = getFutureDate(Integer.parseInt(param.get("day") + ""), Integer.parseInt(param.get("hour") + ""), new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(statWaterExpection.getEndTime()), "yyyy-MM-dd HH:mm:ss");
                    data = getDatebyTime(Double.valueOf(param.get("day") + ""), Double.valueOf(param.get("hour") + ""), 0.0, statWaterExpection.getEndTime(), "1", "yyyy-MM-dd HH:mm:ss");
                } else {
                    //data = getFutureDate(Integer.parseInt(param.get("day") + ""), Integer.parseInt(param.get("hour") + ""), new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(statWaterExpection.getReportTime()), "yyyy-MM-dd HH:mm:ss");
                    data = getDatebyTime(Double.valueOf(param.get("day") + ""), Double.valueOf(param.get("hour") + ""), 0.0, statWaterExpection.getReportTime(), "1", "yyyy-MM-dd HH:mm:ss");
                }
                statWaterExpection.setEndTime(data);
                statWaterExpection.setApproveStatus("0");
                statWaterExpectionMapper.updateById(statWaterExpection);

                log.info("修改水系统异常记录表   暂不处理");
                if (waterAbnormal != null) {
                    waterAbnormal.setProcessResult("暂不处理");
                    iWaterAbnormalService.updateById(waterAbnormal);
                }
                return ResultMsg.getResultMsg("处理成功,等待审批", 200);
            }
        }
        else {
            //水泵
            StatPumpExpection statPumpExpection = statPumpExpectionMapper.selectById(id);
            if (StringUtils.equals(method, "0")) {
                //判断实际是否已处理
                String pointId = statPumpExpection.getPointId();
                String buildingId = statPumpExpection.getBuildingId();
                String expectionType = statPumpExpection.getExpectionType();
                //String expectionStatus = statPumpExpection.getExpectionStatus();
                BaseDevicePoint baseDevicePoint = baseDevicePointMapper.selectById(pointId);
                if (baseDevicePoint != null) {
                    Map<String, String> redisData = (Map<String, String>) JSON.parse((String) redisUtils.get(buildingId + "_pump_" + pointId));
                    //String today = DateUtil.formatDate(new Date(), "yyyy-MM-dd");
//                List<StatPumpEveryday> statPumpEverydays = firePageWindowMapper.selectPumpByBuildingForUpdate(buildingId, today);
                    if (StringUtils.equals(expectionType, "0")) {
                        String outageStatus = redisData.get("outageStatus");
                        if (!StringUtils.equals(outageStatus, "1")) {
                            //未上电
                            return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
                        }
//                    if (statPumpEverydays != null && statPumpEverydays.size() > 0 && !StringUtils.equals(expectionStatus, "2")) {
//                        statPumpEverydays.get(0).setOutageNum(statPumpEverydays.get(0).getOutageNum() - 1);
//                    }
                    } else if (StringUtils.equals(expectionType, "1")) {
                        String manualStatus = redisData.get("manualStatus");
                        if (!StringUtils.equals(manualStatus, "1")) {
                            //手动
                            return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
                        }
//                    if (statPumpEverydays != null && statPumpEverydays.size() > 0 && !StringUtils.equals(expectionStatus, "2")) {
//                        statPumpEverydays.get(0).setManualNum(statPumpEverydays.get(0).getManualNum() - 1);
//                    }
                    } else if (StringUtils.equals(expectionType, "2")) {
                        String operationStatus = redisData.get("operationStatus");
                        if (!StringUtils.equals(operationStatus, "0")) {
                            //运行
                            return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
                        }
//                    if (statPumpEverydays != null && statPumpEverydays.size() > 0 && !StringUtils.equals(expectionStatus, "2")) {
//                        statPumpEverydays.get(0).setRunNum(statPumpEverydays.get(0).getRunNum() - 1);
//                    }
                    } else if (StringUtils.equals(expectionType, "3")) {
                        String faultStatus = redisData.get("faultStatus");
                        if (!StringUtils.equals(faultStatus, "0")) {
                            //故障
                            return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
                        }
//                    if (statPumpEverydays != null && statPumpEverydays.size() > 0 && !StringUtils.equals(expectionStatus, "2")) {
//                        statPumpEverydays.get(0).setFaultNum(statPumpEverydays.get(0).getFaultNum() - 1);
//                    }
                    } else if (StringUtils.equals(expectionType, "4")) {
                        //最近一次心跳时间
                        if ("1".equals(redisData.get("deviceStatus"))) {
                            return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
                        }
//                    long lastHeartbeatTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(redisData.get("lastHeartbeatTime")).getTime();
//                    long nowTime = System.currentTimeMillis();
//                    if (nowTime - lastHeartbeatTime > 50000) {
//                        return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
//                    }
//                    if (statPumpEverydays != null && statPumpEverydays.size() > 0 && !StringUtils.equals(expectionStatus, "2")) {
//                        statPumpEverydays.get(0).setOffNum(statPumpEverydays.get(0).getOffNum() - 1);
//                    }
                    } else if (StringUtils.equals(expectionType, "5")) {
                        //最近一次心跳时间
                        List<PumpData> list = this.checkException(pointId, buildingId, statPumpExpection.getReportTime());
                        if (list == null || list.size() == 0) {
                            return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
                        }
                    }
                    if (redisData != null) {
                        redisData.put("pumpHandleStatus", "1");
                        redisUtils.set(buildingId + "_pump_" + pointId, JSONObject.toJSONString(redisData));
                    }
                }
                //完成处理
                statPumpExpection.setExpectionStatus("1");
                statPumpExpection.setHandlingTime(DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
                statPumpExpection.setHandlingId(userId);
                statPumpExpection.setHandlingName(dataDto.getFullName());
                if (param.get("handlingInfo") != null) {
                    statPumpExpection.setHandlingInfo(String.valueOf(param.get("handlingInfo")));
                }
                statPumpExpectionMapper.updateById(statPumpExpection);

                //更新水泵监测统计实时数据表
                //statPumpEverdayMapper.updateById(statPumpEverydays.get(0));

                log.info("修改水系统异常记录表   处理成功");
                if (waterAbnormal != null) {
                    waterAbnormal.setProcessResult("处理成功");
                    iWaterAbnormalService.updateById(waterAbnormal);
                }
                return ResultMsg.getResultMsg("处理成功", 200);
            } else {

                //之前已经是暂不处理
                if (("2").equals(statPumpExpection.getExpectionStatus())
                        && DateUtil.parseDate(statPumpExpection.getEndTime(), "yyyy-MM-dd HH:mm:ss").getTime() >= System.currentTimeMillis()) {
                    return ResultMsg.getResultMsg("已提交暂不处理,等待审批中无需重复提交。", 500);
                }

                //暂不处理
                //更新水泵监测统计实时数据表 对应减1
                String buildingId = statPumpExpection.getBuildingId();
                String pointId = statPumpExpection.getPointId();

                //调用审批流程接口
                Double day = Double.valueOf(String.valueOf(param.get("day")));
                day = statPumpExpection.getDay() == null ? day : (Double.valueOf(statPumpExpection.getDay()) + day);
                Double hour = Double.valueOf(String.valueOf(param.get("hour")));
                hour = statPumpExpection.getHour() == null ? hour : (Double.valueOf(statPumpExpection.getHour()) + hour);
                if (hour > 24) {
                    day = day + (hour / 24);
                    hour = hour % 24;
                }


                //查水泵基础时长配置表
                List<Map<String, Object>> configRelation = configRelationMapper.selectByConfigCode("splc_1_jcsc");
                //数据库天数存在小时字段里了
                Double dayTime = Double.valueOf(configRelation.get(0).get("config_int_day") + "");
                Double hourTime = Double.valueOf(configRelation.get(0).get(ConfigRelationUtil.CON_INT_HOUR) + "");

                if (day > dayTime || (day <= dayTime && hour > (dayTime * 24 + hourTime))) {
                    //超过基础时长
                    approve(request, "1326050131358777346", buildingId, pointId, id, param);
                } else {
                    //未超过
                    approve(request, "1325986921079373825", buildingId, pointId, id, param);
                }

                //审批成功后对统计数量进行减少
                /*List<StatPumpEveryday> statPumpEverydays = firePageWindowMapper.selectPumpByBuildingForUpdate(buildingId, today);
                if (StringUtils.equals(expectionType, "0")) {
                    if (statPumpEverydays != null && statPumpEverydays.size() > 0 && !StringUtils.equals(expectionStatus, "2")) {
                        statPumpEverydays.get(0).setOutageNum(statPumpEverydays.get(0).getOutageNum() - 1);
                    }
                } else if (StringUtils.equals(expectionType, "1")) {
                    if (statPumpEverydays != null && statPumpEverydays.size() > 0 && !StringUtils.equals(expectionStatus, "2")) {
                        statPumpEverydays.get(0).setManualNum(statPumpEverydays.get(0).getManualNum() - 1);
                    }
                } else if (StringUtils.equals(expectionType, "2")) {
                    if (statPumpEverydays != null && statPumpEverydays.size() > 0 && !StringUtils.equals(expectionStatus, "2")) {
                        statPumpEverydays.get(0).setRunNum(statPumpEverydays.get(0).getRunNum() - 1);
                    }
                } else if (StringUtils.equals(expectionType, "3")) {
                    if (statPumpEverydays != null && statPumpEverydays.size() > 0 && !StringUtils.equals(expectionStatus, "2")) {
                        statPumpEverydays.get(0).setFaultNum(statPumpEverydays.get(0).getFaultNum() - 1);
                    }
                } else if (StringUtils.equals(expectionType, "4")) {
                    if (statPumpEverydays != null && statPumpEverydays.size() > 0 && !StringUtils.equals(expectionStatus, "2")) {
                        statPumpEverydays.get(0).setOffNum(statPumpEverydays.get(0).getOffNum() - 1);
                    }
                }

                //更新每日统计表
                //statPumpEverdayMapper.updateById(statPumpEverydays.get(0));*/


                statPumpExpection.setExpectionStatus("2");
                statPumpExpection.setHandlingInfo(param.get("handlingInfo") == null ? "" : String.valueOf(param.get("handlingInfo")));
                statPumpExpection.setReason(param.get("reason") == null ? "" : String.valueOf(param.get("reason")));
                statPumpExpection.setHandlingTime(DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
                statPumpExpection.setHandlingId(userId);
                statPumpExpection.setHandlingName(dataDto.getFullName());
                if (day != null) {
                    statPumpExpection.setDay(String.valueOf(param.get("day")));
                }
                if (hour != null) {
                    statPumpExpection.setHour(String.valueOf(param.get("hour")));
                }
                String data = null;
                /*if (StringUtils.isNoneBlank(statPumpExpection.getEndTime())) {
                    data = getFutureDate(Integer.parseInt(param.get("day") + ""), Integer.parseInt(param.get("hour") + ""), new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(statPumpExpection.getEndTime()), "yyyy-MM-dd HH:mm:ss");
                } else {
                    data = getFutureDate(Integer.parseInt(param.get("day") + ""), Integer.parseInt(param.get("hour") + ""), new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(statPumpExpection.getReportTime()), "yyyy-MM-dd HH:mm:ss");
                }*/
                if (StringUtils.isNoneBlank(statPumpExpection.getEndTime())) {
                    //data = getFutureDate(Integer.parseInt(param.get("day") + ""), Integer.parseInt(param.get("hour") + ""), new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(statWaterExpection.getEndTime()), "yyyy-MM-dd HH:mm:ss");
                    data = getDatebyTime(Double.valueOf(param.get("day") + ""), Double.valueOf(param.get("hour") + ""), 0.0, statPumpExpection.getEndTime(), "1", "yyyy-MM-dd HH:mm:ss");
                } else {
                    //data = getFutureDate(Integer.parseInt(param.get("day") + ""), Integer.parseInt(param.get("hour") + ""), new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(statWaterExpection.getReportTime()), "yyyy-MM-dd HH:mm:ss");
                    data = getDatebyTime(Double.valueOf(param.get("day") + ""), Double.valueOf(param.get("hour") + ""), 0.0, statPumpExpection.getReportTime(), "1", "yyyy-MM-dd HH:mm:ss");
                }
                statPumpExpection.setEndTime(data);
                statPumpExpection.setApproveStatus("0");
                statPumpExpectionMapper.updateById(statPumpExpection);

                log.info("修改水系统异常记录表   暂不处理");
                if (waterAbnormal != null) {
                    waterAbnormal.setProcessResult("暂不处理");
                    iWaterAbnormalService.updateById(waterAbnormal);
                }
                return ResultMsg.getResultMsg("处理成功,等待审批", 200);
            }
        }
    }


    @Override
    public ResultMsg updateCabinetExpectionHandle(HttpServletRequest request, Map<String, Object> param) {
        String module = String.valueOf(param.get("module"));
        String id = String.valueOf(param.get("id"));
        String method = String.valueOf(param.get("method"));
        ResultMsg rm = new ResultMsg();
        if (id.contains(",")) {
            String[] ids = id.split(",");
            for (String s : ids) {
                rm = handleCabinetExpHandleInfo(request, s, method, module, param);
                if (rm.getCode() == 10000) {
                    rm = ResultMsg.getResultMsg("处理成功,等待审批", 200);
                    continue;
                }
            }
        } else {
            rm = handleCabinetExpHandleInfo(request, id, method, module, param);
        }
        return rm;
    }

    private ResultMsg handleCabinetExpHandleInfo(HttpServletRequest request, String id, String method, String module, Map<String, Object> param) {

        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        String userId = request.getHeader("xfUserId");//消防userId
        UsersResponse.DataDTO dataDto = orgMiddleService.findUserByUserId(wztUserId);
        QueryWrapper<WaterAbnormal> queryWrapper = new QueryWrapper<WaterAbnormal>();
        queryWrapper.eq("abnirmal_id", id);
        WaterAbnormal waterAbnormal = iWaterAbnormalService.getOne(queryWrapper);
        log.info("水系统异常记录数据--->" + JSON.toJSONString(waterAbnormal));
        if (waterAbnormal != null) {
            waterAbnormal.setProcessor(dataDto.getFullName());
            if (StringUtils.equals(method, "0")) {
                waterAbnormal.setProcessTime(LocalDateTime.now());
            }
        }


        StatCabinetExpection statPumpExpection = statCabinetExpectionMapper.selectById(id);
        String pointId = statPumpExpection.getPointId();
        String buildingId = statPumpExpection.getBuildingId();
        String expectionType = statPumpExpection.getExpectionType();
        BaseDevicePoint baseDevicePoint = baseDevicePointMapper.selectById(pointId);
        if (baseDevicePoint != null) {
            Map<String, String> redisData = (Map<String, String>) JSON.parse((String) redisUtils.get(buildingId + "_cabinet_" + pointId));
            if(redisData != null && !redisData.isEmpty()){
                if (StringUtils.equals(expectionType, "0")) {
                    String outageStatus = redisData.get("outageStatus");
                    if (!StringUtils.equals(outageStatus, "1")) {
                        //未上电
                        return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
                    }
                } else if (StringUtils.equals(expectionType, "1")) {
                    String manualStatus = redisData.get("manualStatus");
                    if (!StringUtils.equals(manualStatus, "1")) {
                        return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
                    }
                } else if (StringUtils.equals(expectionType, "2")) {
                    String operationStatus = redisData.get("operationStatus");
                    if (!StringUtils.equals(operationStatus, "0")) {
                        //运行
                        return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
                    }
                } else if (StringUtils.equals(expectionType, "3")) {
                    String faultStatus = redisData.get("faultStatus");
                    if (!StringUtils.equals(faultStatus, "0")) {
                        //故障
                        return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
                    }
                } else if (StringUtils.equals(expectionType, "4")) {
                    //最近一次心跳时间
                    if ("1".equals(redisData.get("deviceStatus"))) {
                        return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
                    }
                } else if (StringUtils.equals(expectionType, "5")) {
                    //最近一次心跳时间
                    List<PumpData> list = this.checkException(pointId, buildingId, statPumpExpection.getReportTime());
                    if (list == null || list.size() == 0) {
                        return ResultMsg.getResultMsg("处理未完成，请确保设备正常后稍后再试", 10000);
                    }
                }
            }
            if (redisData != null) {
                redisData.put("pumpHandleStatus", "1");
                redisUtils.set(buildingId + "_cabinet_" + pointId, JSONObject.toJSONString(redisData));
            }
        }
        //完成处理
        statPumpExpection.setExpectionStatus("1");
        statPumpExpection.setHandlingTime(DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
        statPumpExpection.setHandlingId(userId);
        statPumpExpection.setHandlingName(dataDto.getFullName());
        if (param.get("handlingInfo") != null) {
            statPumpExpection.setHandlingInfo(String.valueOf(param.get("handlingInfo")));
        }
        statCabinetExpectionMapper.updateById(statPumpExpection);

        //更新水泵监测统计实时数据表
        //statPumpEverdayMapper.updateById(statPumpEverydays.get(0));

        log.info("修改水系统异常记录表   处理成功");
        if (waterAbnormal != null) {
            waterAbnormal.setProcessResult("处理成功");
            iWaterAbnormalService.updateById(waterAbnormal);
        }
        return ResultMsg.getResultMsg("处理成功", 200);

    }

    public void approve(HttpServletRequest request, String defKey, String buildingId, String pointId, String id, Map param) {
        Map<String, Object> approve = new HashMap<>();
        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        String userId = request.getHeader("xfUserId");//消防userId
        UsersResponse.DataDTO dataDto = orgMiddleService.findUserByUserId(wztUserId);
        Map<String, YssqbDto> yssqb = new HashMap<>();
        BaseBuilding baseBuilding = baseBuildingMapper.selectById(buildingId);
        BaseDevicePoint baseDevicePoint = baseDevicePointMapper.selectById(pointId);
        YssqbDto yssqbDto = new YssqbDto();
        yssqbDto.setJzwid(buildingId);
        yssqbDto.setJzwmc(baseBuilding.getBuildingName());
        yssqbDto.setDwid(pointId);
        yssqbDto.setDwmc(baseDevicePoint == null ? "无信息" : baseDevicePoint.getDevTypeName());
        yssqbDto.setYcid(id);
        yssqbDto.setSblx(baseDevicePoint == null ? "无信息" : baseDevicePoint.getDevTypeCode());
        yssqbDto.setYssj(String.valueOf(param.get("day")));
        yssqbDto.setYsxs(String.valueOf(param.get("hour")));
        yssqbDto.setYsclyy(String.valueOf(param.get("reason")));
        yssqbDto.setFqr(dataDto.getFullName());
        approve.put("checkType", "AGREE");
        approve.put("defKey", defKey);
        yssqb.put("yssqb", yssqbDto);
        approve.put("formJson", JSONObject.toJSONString(yssqb));
        System.out.println("审批流参数：" + JSONObject.toJSONString(yssqb));
        System.out.println("审批流json：" + JSONObject.parseObject(JSONObject.toJSONString(approve)));
        JsonResult<BpmInst> jsonResult = bpmClient.startProcess(JSONObject.parseObject(JSONObject.toJSONString(approve)));
        JSONObject.parseObject("", yssqbDto.getClass());
        log.info("流程返回结果为：{}", JSONObject.toJSONString(jsonResult));
        try {
//            bpmForwardService.sendMiddleApprove(jsonResult.getData().getInstId(), new YssqbDto(), null, null);
        } catch (Exception e) {
            e.printStackTrace();
//            throw new RuntimeException("发送流程到中台出错");
        }

    }

    /**
     * 暂不处理审批结束后更新状态
     *
     * @param request type( 0 水压 1 水泵) result(0 审批通过 1 审批不通过)
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public synchronized ResultMsg afterApprover(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        String type = (String) param.get("type");
        String result = (String) param.get("resupt");
        String id = (String) param.get("id");
        if (StringUtils.equals(type, "0")) {
            //水压
            StatWaterExpection statWaterExpection = statWaterExpectionMapper.selectById(id);
            if (StringUtils.equals(result, "0")) {
                //审批通过
                Integer day = Integer.parseInt(statWaterExpection.getDay());
                Integer hour = Integer.parseInt(statWaterExpection.getHour());
                String reportTime = statWaterExpection.getReportTime();
                String endTime = null;
                try {
                    Date date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(reportTime);
                    //生成暂不处理结束时间
                    endTime = getFutureDate(day, hour, date, "yyyy-MM-dd HH:mm:ss");
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                statWaterExpection.setEndTime(endTime);
                //非完成处理的情况去更新（用于设备自动处理过之后通过审核这里不做处理)
                if (!"1".equals(statWaterExpection.getExpectionStatus())) {
                    statWaterExpectionMapper.updateById(statWaterExpection);
                }


            } else {
                //审批未通过
                statWaterExpection.setExpectionStatus("0");
                //非完成处理的情况去更新（用于设备自动处理过之后通过审核这里不做处理）
                if (!"1".equals(statWaterExpection.getExpectionStatus())) {
                    statWaterExpectionMapper.updateById(statWaterExpection);
                }
            }
        } else {
            //水泵
            StatPumpExpection statPumpExpection = statPumpExpectionMapper.selectById(id);
            if (StringUtils.equals(result, "0")) {
                //审批通过
                Integer day = Integer.parseInt(statPumpExpection.getDay());
                Integer hour = Integer.parseInt(statPumpExpection.getHour());
                String reportTime = statPumpExpection.getReportTime();
                String endTime = null;
                try {
                    Date date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(reportTime);
                    endTime = getFutureDate(day, hour, date, "yyyy-MM-dd HH:mm:ss");
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                statPumpExpection.setEndTime(endTime);
                //非完成处理的情况去更新（用于设备自动处理过之后通过审核这里不做处理）
                if (!"1".equals(statPumpExpection.getExpectionStatus())) {
                    statPumpExpectionMapper.updateById(statPumpExpection);
                }
            } else {
                //审批未通过
                statPumpExpection.setExpectionStatus("0");
                //非完成处理的情况去更新（用于设备自动处理过之后通过审核这里不做处理）
                if (!"1".equals(statPumpExpection.getExpectionStatus())) {
                    statPumpExpectionMapper.updateById(statPumpExpection);
                }
            }
        }
        return ResultMsg.getResultMsg("状态已更新", 10000);
    }

    /**
     * 获取误报统计信息
     *
     * @param request
     * @return
     */
    @Override
    public List<Map<String, Object>> getWBStatistic(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);
//        if (CheckChineseUtil.isBtSquare(MapUtils.getString(param,"buildingId"))){
//            return waiXiaoService.getWBStatistic(MapUtils.getString(param,"buildingId"),MapUtils.getString(param,"period"));
//        }
        String period = String.valueOf(param.get("period"));
        //查询火警总点位数量
        List<String> periotList = null;
        if (StringUtils.equals(period, "0")) {
            //统计7天
            periotList = getPeriotDate(7);
        } else {
            //统计30天
            periotList = getPeriotDate(30);
        }
        List<Map<String, Object>> result = new ArrayList<>();
        //2为反复误报
        param.put("types", "2");
        for (String p : periotList) {
            param.put("todayDate", p);
            List<Map<String, Object>> faultFireStatistics = faultFireStatisticsMapper.selectStatisticsByDateList(param);
            if (!CollectionUtils.isEmpty(faultFireStatistics)) {
                result.add(faultFireStatistics.get(0));
            } else {
                Map<String, Object> map = new HashMap<>();
                map.put("day", p);
                map.put("alarmNum", "0");
                map.put("pointNum", "0");
                map.put("alarmRate", "0");
                result.add(map);
            }
        }
        return result;
    }

    //查询当前日期前几天的日期
    @Override
    public List<String> getPeriotDate(int num) {
        List<String> list = new ArrayList<>();
        for (int i = num; i > 0; i--) {
            list.add(getPastDate(i, "yyyy-MM-dd"));
        }
        return list;
    }

    /**
     * 获取故障统计信息
     *
     * @param request
     * @return
     */
    @Override
    public List<Map<String, Object>> getFaultStatistic(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        String period = String.valueOf(param.get("period"));
        String buildingId = String.valueOf(param.get("buildingId"));
//        if (CheckChineseUtil.isBtSquare(buildingId)){
//            return waiXiaoService.getFaultStatistic(buildingId,period);
//        }
        //查询火警总点位数量
        //int num = baseDevicePointMapper.selectCount(new QueryWrapper<BaseDevicePoint>().eq("super_type", "0").eq("building_id", buildingId));
        List<String> periotList = null;
        if (StringUtils.equals(period, "0")) {
            //统计7天
            periotList = getPeriotDate(7);
        } else {
            //统计30天
            periotList = getPeriotDate(30);
        }
        List<Map<String, Object>> result = new ArrayList<>();

        param.put("types", "1");
        for (String p : periotList) {
            param.put("todayDate", p);
            List<Map<String, Object>> faultFireStatistics = faultFireStatisticsMapper.selectStatisticsByDateList(param);
            if (!CollectionUtils.isEmpty(faultFireStatistics)) {
                result.add(faultFireStatistics.get(0));
            } else {
                Map<String, Object> map = new HashMap<>();
                map.put("day", p);
                map.put("faultNum", "0");
                map.put("faultPointNum", "0");
                map.put("faultRate", "0");
                result.add(map);
            }
        }

//        for (String day : periotList) {
//            Map<String, Object> map = new HashMap<>();
//            //当天时间范围
//            //param.put("startTime", periotList.get(0) + " 00:00:00");
//            param.put("endTime", day + " 23:59:59");
//            int faultNum = faultInfoMapper.selectCount(new QueryWrapper<FaultInfo>()
//                    .eq("building_id", buildingId).ge("last_time", day + " 00:00:00").le("last_time", day + " 23:59:59"));
//            //查询历史未处、处理中理故障
//            int faultPointNum = firePageWindowMapper.selectFaultPointNum(param);
//
//            //故障率
//            String faultRate = "0";
//            if (faultPointNum != 0 && num != 0) {
//                //计算结果乘100，四舍五入保留10位小数
//                faultRate = new BigDecimal(faultPointNum + "").divide(new BigDecimal(num + ""), 10, RoundingMode.HALF_UP).multiply(new BigDecimal(100)) + "";
//            }
//
//            map.put("day", day);
//            map.put("faultNum", faultNum + "");
//            map.put("faultPointNum", faultPointNum + "");
//            map.put("faultRate", faultRate);
//            result.add(map);
//        }
        return result;
    }

//    @Override
//    public String uploadPic(MultipartFile uploadFile) {
//        return fileService.saveFile("firpic", uploadFile).getMessage();
//    }

    /**
     * 获取已有时间和图片路径
     *
     * @param fireId
     * @param duration
     * @param paths
     */
    public Map<String, String> setPicAndTime(String fireId, String duration, String paths) {

        Map<String, String> map = new HashMap<>();
        List<Map<String, Object>> timeAndPath = fireInfoMapper.getByFireid(fireId);

        //当该火警已填报两张照片,使用原有填报照片
        if (timeAndPath.size() > 1) {
            duration = timeAndPath.get(0).get("checkTime").toString();
            if (Objects.nonNull(timeAndPath.get(0).get("path")) && Objects.nonNull(timeAndPath.get(1).get("path"))) {
                paths = timeAndPath.get(0).get("path") + "," + timeAndPath.get(1).get("path");
            }
        }
        if (timeAndPath.size() > 2) {
            if (Objects.nonNull(timeAndPath.get(2).get("path"))) {
                paths = paths + "," + timeAndPath.get(2).get("path");
            }
        }
        map.put("duration", duration);
        map.put("paths", paths);
        return map;

    }


    /**
     * 误报火警提交
     *
     * @param
     */
    @Override
    public void falsePositiveReported(HttpServletRequest request, Map<String, Object> param) {
        String isWalkingStreet = (String) param.get("isWalkingStreet");
        String fireRemark = (String) param.get("fireRemark");
        String checkUser = (String) param.get("checkUser");
        String fireId = (String) param.get("id");
        String paths = (String) param.get("paths");
        String checkImgJson = (String) param.get("checkImgJson");
        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        String userId = request.getHeader("xfUserId");//消防userId
        UsersResponse.DataDTO dataDto = orgMiddleService.findUserByUserId(wztUserId);
        String msg = null;
        if (StringUtils.isEmpty(checkUser)) {
            msg = "核实人员不能为空";
        }
        if (StringUtils.isEmpty(paths)) {
            msg = "照片不能为空";
        }
        if (StringUtils.isNotEmpty(msg)) {
            throw new RuntimeException(msg);
        }
        String duration = "0";
        if (param.get("autoCheckTime") != null && !"".equals(param.get("autoCheckTime"))) {
            duration = param.get("autoCheckTime") + "";
        } else if (param.get("checkTime") != null && !"".equals(param.get("checkTime"))) {
            duration = param.get("checkTime") + "";
        }

        Map<String, String> picAndTime = setPicAndTime(fireId, duration, paths);


        FireInfo fireInfo = fireInfoMapper.selectInfoById(fireId);
        BaseDevicePoint baseDevicePoint = baseDevicePointMapper.selectById(fireInfo.getPointId());
        if (baseDevicePoint != null && baseDevicePoint.getPointDesc() != null && "待补全".equals(baseDevicePoint.getPointDesc())) {
//            log.info("代补全点位不做处理：" + baseDevicePoint.getPointNumber());
//            return ;
        }
        //List<Check> checks = iCheckService.queryByFireId(fireId);
        if (param.get("checkerId") != null && !"".equals(param.get("checkerId"))) {
            fireInfo.setCheckerId(param.get("checkerId").toString());
        }
        fireInfo.setFillUser(dataDto.getFullName());
        fireInfo.setFillUserId(userId);
        fireInfo.setReportStatus("1");
        fireInfo.setFeedbackResult("误报火警");//反馈结果
        fireInfo.setFillInTime(DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
        fireInfo.setIsWalkingStreet(isWalkingStreet);
        fireInfo.setFireRemark(fireRemark);
        fireInfo.setChecker(checkUser);
        fireInfo.setReportSituation("已填报");
        fireInfo.setCheckImg(picAndTime.get("paths"));
        fireInfo.setCheckImgJson(checkImgJson);
        log.info("误报火警提交---------------falsePositiveReported:duration:"+picAndTime.get("duration")+",入参:"+JSON.toJSONString(param));
        fireInfo.setDuration(picAndTime.get("duration"));
        BaseBuilding baseBuilding1 = baseBuildingMapper.selectById(fireInfo.getBuildingId());
        fireInfo.setCreateDepId(baseBuilding1.getBelongDep());
        fireInfo.setBelongDep(baseBuilding1.getBelongDep());
        if (fireInfo.getFeedbackTime() == null || "".equals(fireInfo.getFeedbackTime())) {
            fireInfo.setFeedbackTime(LocalDateTime.now());
        }
        //更新redis状态FastJSONUtils.toBean(redisUtils.get("point_status:"+fireInfo.getBuildingId() + "-" + fireInfo.getPointId()) +"", FarEastoneCache.class)
        FarEastoneCache farEastoneCache = FastJSONUtils.toBean(redisUtils.get("point_status:" + fireInfo.getBuildingId() + "-" + fireInfo.getPointId()) + "", FarEastoneCache.class);
        if (farEastoneCache != null) {
            farEastoneCache.setFireHandleStatus("1");
            redisUtils.set("point_status:" + fireInfo.getBuildingId() + "-" + fireInfo.getPointId(), JSON.toJSONString(farEastoneCache));
        }
        /*if (checks != null && checks.size() > 0) {
            Check check = checks.get(0);
            fireInfo.setFeedbackTime(LocalDateTime.parse(StringUtils.isEmpty(check.getUploadTime()) ? DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss") : check.getUploadTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            fireInfo.setDuration(String.valueOf(check.getCheckTime()));
        }*/
        getGrade(fireInfo);
        fireInfoMapper.updateById(fireInfo);
        BaseBuilding baseBuilding = baseBuildingMapper.selectById(fireInfo.getBuildingId());
        if (StringUtils.equals("0", isWalkingStreet)) {
            //不是步行街点位标准用时150s
            if (Integer.parseInt(fireInfo.getDuration()) > 150) {
                //跑点超时短息通知
                timeTaskService.paodianOutTimeShortNote(fireInfo, baseBuilding);
            }
        } else {
            //是步行街点位标准用时180s
            //跑点超时短息通知
            if (Integer.parseInt(fireInfo.getDuration()) > 180) {
                timeTaskService.paodianOutTimeShortNote(fireInfo, baseBuilding);
            }
        }
    }

    /**
     * 测试/确认火警提交
     *
     * @param
     */
    @Override
    public void testFireReported(HttpServletRequest request, Map<String, Object> param) {
        String typeSubdivision = (String) param.get("typeSubdivision");
        String fireId = (String) param.get("id");
        String videoUrlJson = (String) param.get("videoUrlJson");
        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        String xfUserId = request.getHeader("xfUserId");//消防userId
        UsersResponse.DataDTO dataDto = orgMiddleService.findUserByUserId(wztUserId);
        FireInfo fireInfo = fireInfoMapper.selectInfoById(fireId);
        BaseDevicePoint baseDevicePoint = baseDevicePointMapper.selectById(fireInfo.getPointId());
        if (baseDevicePoint != null && baseDevicePoint.getPointDesc() != null && "待补全".equals(baseDevicePoint.getPointDesc())) {
//            log.info("代补全点位不做处理：" + baseDevicePoint.getPointNumber());
//            return ;
        }
        BaseBuilding baseBuilding1 = baseBuildingMapper.selectById(fireInfo.getBuildingId());
        fireInfo.setCreateDepId(baseBuilding1.getBelongDep());
        fireInfo.setBelongDep(baseBuilding1.getBelongDep());
        fireInfo.setFillUser(dataDto.getFullName());
        fireInfo.setFillUserId(xfUserId);

        //如果现场处置人员未反馈则设置反馈时间
        if (fireInfo.getFeedbackTime() == null || "".equals(fireInfo.getFeedbackTime())) {
            fireInfo.setFeedbackTime(LocalDateTime.now());
        }
        List<Check> checks = iCheckService.queryByFireId(fireId);
        String duration = "0";
        String paths = "";
        if ("0".equals(typeSubdivision)) {
            //设备测试
            fireInfo.setTypeSubdivision("0");
            fireInfo.setTypeSubdivisionStr("设备测试");
            //更新填报状态
            fireInfo.setReportStatus("1");
            fireInfo.setFillInTime(DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
            fireInfo.setReportSituation("已填报");
            fireInfo.setTestType("0");
            fireInfo.setTestTypeStr("设备测试");
            fireInfo.setVideoUrlJson(videoUrlJson);

            if (param.get("autoCheckTime") != null && !"".equals(param.get("autoCheckTime"))) {
                duration = param.get("autoCheckTime") + "";
            } else if (param.get("checkTime") != null && !"".equals(param.get("checkTime"))) {
                duration = param.get("checkTime") + "";
            }

            /*if (checks != null && checks.size() > 0) {
                Check check = checks.get(0);
                fireInfo.setFeedbackTime(LocalDateTime.parse(StringUtils.isEmpty(check.getUploadTime()) ? DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss") : check.getUploadTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                fireInfo.setDuration(String.valueOf(check.getCheckTime()));
            }*/
            Map<String, String> picAndTime = setPicAndTime(fireId, duration, paths);
            log.info("确认火警提交----------------typeSubdivision.equals(0)testFireReported：duration："+picAndTime.get("duration")+",入参param："+JSON.toJSONString(param));
            fireInfo.setDuration(picAndTime.get("duration"));

            fireInfoMapper.updateById(fireInfo);
        } else if (typeSubdivision.equals("1")) {
            fireInfo.setTypeSubdivision("1");
            fireInfo.setTypeSubdivisionStr("跑点测试");
            //更新填报状态
            fireInfo.setReportStatus("1");
            fireInfo.setFillInTime(DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
            if (param.get("testUnit") != null && !"".equals(param.get("testUnit"))) {
                fireInfo.setTestUnit((String) param.get("testUnit"));
            }
            if (param.get("tester") != null && !"".equals(param.get("tester"))) {
                fireInfo.setTester((String) param.get("tester"));
            }
            if (param.get("organizer") != null && !"".equals(param.get("organizer"))) {
                fireInfo.setOrganizer((String) param.get("organizer"));
            }
            if (param.get("checker") != null && !"".equals(param.get("checker"))) {
                fireInfo.setChecker((String) param.get("checker"));
            }
            if (param.get("checkerId") != null && !"".equals(param.get("checkerId"))) {
                fireInfo.setCheckerId(param.get("checkerId").toString());
            }
            if (param.get("isWalkingStreet") != null && !"".equals(param.get("isWalkingStreet"))) {
                fireInfo.setIsWalkingStreet((String) param.get("isWalkingStreet"));
            }
            if (param.get("cause") != null && !"".equals(param.get("cause"))) {
                fireInfo.setCause((String) param.get("cause"));
            }

            if (param.get("paths") != null && !"".equals(param.get("paths"))) {
                paths = param.get("paths") + "";
            }

            if (param.get("autoCheckTime") != null && !"".equals(param.get("autoCheckTime"))) {
                duration = param.get("autoCheckTime") + "";
            } else if (param.get("checkTime") != null && !"".equals(param.get("checkTime"))) {
                duration = param.get("checkTime") + "";
            }

            fireInfo.setReportSituation("已填报");
            Map<String, String> picAndTime = setPicAndTime(fireId, duration, paths);
            log.info("确认火警提交----------------typeSubdivision.equals(1)testFireReported：picAndTime："+JSON.toJSONString(picAndTime)+",入参param："+JSON.toJSONString(param));
            fireInfo.setDuration(picAndTime.get("duration"));
            fireInfo.setCheckImg(picAndTime.get("paths"));

            getGrade(fireInfo);
            fireInfoMapper.updateById(fireInfo);

            BaseBuilding baseBuilding = baseBuildingMapper.selectById(fireInfo.getBuildingId());
            if (StringUtils.equals("0", param.get("isWalkingStreet") + "")) {
                //不是步行街点位标准用时150s
                if (Integer.parseInt(fireInfo.getDuration()) > 150) {
                    //跑点超时短息通知
                    timeTaskService.paodianOutTimeShortNote(fireInfo, baseBuilding);
                }
            } else {
                //是步行街点位标准用时180s
                //跑点超时短息通知
                if (Integer.parseInt(fireInfo.getDuration()) > 180) {
                    timeTaskService.paodianOutTimeShortNote(fireInfo, baseBuilding);
                }
            }
        } else if (typeSubdivision.equals("2")) {
            fireInfo.setTypeSubdivision("2");
            fireInfo.setTypeSubdivisionStr("真实火警");
            //更新填报状态
            fireInfo.setReportStatus("1");
            fireInfo.setFillInTime(DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
            if (param.get("videoUrl") != null && !"".equals(param.get("videoUrl"))) {
                fireInfo.setVideoUrl((String) param.get("videoUrl"));
            }
            if (param.get("fireReason") != null && !"".equals(param.get("fireReason"))) {
                fireInfo.setCause((String) param.get("fireReason"));
            }

            if (param.get("paths") != null && !"".equals(param.get("paths"))) {
                paths = param.get("paths") + "";
            }
            if (param.get("autoCheckTime") != null && !"".equals(param.get("autoCheckTime"))) {
                duration = param.get("autoCheckTime") + "";
            } else if (param.get("checkTime") != null && !"".equals(param.get("checkTime"))) {
                duration = param.get("checkTime") + "";
            }

            fireInfo.setReportSituation("已填报");

            Map<String, String> picAndTime = setPicAndTime(fireId, duration, paths);
            log.info("确认火警提交----------------typeSubdivision.equals(2)testFireReported：picAndTime："+JSON.toJSONString(picAndTime)+",入参param："+JSON.toJSONString(param));
            fireInfo.setDuration(picAndTime.get("duration"));
            fireInfo.setCheckImg(picAndTime.get("paths"));

            fireInfo.setUploadStatus((String) param.get("uploadStatus"));
            getGrade(fireInfo);
            fireInfoMapper.updateById(fireInfo);
            // 上传真实火警到发送端, 等于1的话，要对火警信息进行上传
            if ("1".equals(param.get("uploadStatus"))) {
                Map<String, Object> result = new HashMap<>();
                result.put("data", fireInfo);
                this.sendMessage(result);
            }
        } else if (typeSubdivision.equals("3")) {
            fireInfo.setTypeSubdivision("3");
            fireInfo.setTypeSubdivisionStr("演示测试");
            //更新填报状态
            fireInfo.setReportStatus("1");
            fireInfo.setFillInTime(DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
            if (param.get("testUnit") != null && !"".equals(param.get("testUnit"))) {
                fireInfo.setTestUnit((String) param.get("testUnit"));
            }
            if (param.get("tester") != null && !"".equals(param.get("tester"))) {
                fireInfo.setTester((String) param.get("tester"));
            }
            if (param.get("organizer") != null && !"".equals(param.get("organizer"))) {
                fireInfo.setOrganizer((String) param.get("organizer"));
            }
            if (param.get("checker") != null && !"".equals(param.get("checker"))) {
                fireInfo.setChecker((String) param.get("checker"));
            }
            if (param.get("checkerId") != null && !"".equals(param.get("checkerId"))) {
                fireInfo.setCheckerId(param.get("checkerId").toString());
            }
            if (param.get("isWalkingStreet") != null && !"".equals(param.get("isWalkingStreet"))) {
                fireInfo.setIsWalkingStreet((String) param.get("isWalkingStreet"));
            }
            if (param.get("cause") != null && !"".equals(param.get("cause"))) {
                fireInfo.setCause((String) param.get("cause"));
            }

            fireInfo.setReportSituation("已填报");

            if (param.get("paths") != null && !"".equals(param.get("paths"))) {
                paths = param.get("paths") + "";
            }
            if (param.get("autoCheckTime") != null && !"".equals(param.get("autoCheckTime"))) {
                duration = param.get("autoCheckTime") + "";
            } else if (param.get("checkTime") != null && !"".equals(param.get("checkTime"))) {
                duration = param.get("checkTime") + "";
            }

            Map<String, String> picAndTime = setPicAndTime(fireId, duration, paths);
            log.info("确认火警提交----------------typeSubdivision.equals(3)testFireReported：picAndTime："+JSON.toJSONString(picAndTime)+",入参param："+JSON.toJSONString(param));
            fireInfo.setDuration(picAndTime.get("duration"));
            fireInfo.setCheckImg(picAndTime.get("paths"));

            getGrade(fireInfo);
            fireInfoMapper.updateById(fireInfo);

            BaseBuilding baseBuilding = baseBuildingMapper.selectById(fireInfo.getBuildingId());
            if (StringUtils.equals("0", param.get("isWalkingStreet") + "")) {
                //不是步行街点位标准用时150s
                if (Integer.parseInt(fireInfo.getDuration()) > 150) {
                    //跑点超时短息通知
                    timeTaskService.paodianOutTimeShortNote(fireInfo, baseBuilding);
                }
            } else {
                //是步行街点位标准用时180s
                //跑点超时短息通知
                if (Integer.parseInt(fireInfo.getDuration()) > 180) {
                    timeTaskService.paodianOutTimeShortNote(fireInfo, baseBuilding);
                }
            }
        }
    }


    private void getGrade(FireInfo fireInfo) {
        Long T = 180L;
        Long duration = Long.parseLong(fireInfo.getDuration());
        if ("1".equals(fireInfo.getIsWalkingStreet())) {
            T = 150L;
        }
        List<Map<String, Object>> time = null;
        if (duration > 0 && duration <= (T - 20)) {
            time = configRelationMapper.selectByConfigCode("pfsz_2_0");
        } else if (duration > (T - 20) && duration <= T) {
            time = configRelationMapper.selectByConfigCode("pfsz_2_1");
        } else if (duration <= (T + 120) && duration > T) {
            time = configRelationMapper.selectByConfigCode("pfsz_2_2");
        } else if (duration > (T + 120)) {
            time = configRelationMapper.selectByConfigCode("pfsz_2_3");
        }
        if (time != null) {
            fireInfo.setGrade(time.get(0).get(ConfigRelationUtil.CON_INT_MIN) + "");
        }
    }

    @Override
    public List<WebsocketInfo> getFloorPlanExpection(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        //参数添加起止时间
        paramAddDate(param);
        //查火警
        param.put("type", "0");
        List<FireInfo> fireInfos = fireInfoMapper.selectFireExpection(param);
        //查预警
        param.remove("type");
        param.put("type", "6");
        List<FireInfo> waringFireInfos = fireInfoMapper.selectFireExpection(param);
        List<Map<String, Object>> StatWaterExpections = firePageWindowMapper.selectWaterExpection(param);
        List<Map<String, Object>> statPumpExpections = firePageWindowMapper.selectPumpExpection(param);
        param.put("faultStatus", "0");
        List<Map<String, Object>> faultInfos = firePageWindowMapper.selectTodayFaultInfo(param);
        List<WebsocketInfo> result = new ArrayList<>();
        if (fireInfos != null && fireInfos.size() > 0) {
            // 先获取所有的点位id
            List<String> pids = fireInfos.stream().map(FireInfo::getPointId).collect(Collectors.toList());
            List<BaseDevicePoint> baseDevicePoints = baseDevicePointMapper.selectBatchIds(pids);
            if (!CollectionUtils.isEmpty(baseDevicePoints)) {
                Map<String, List<BaseDevicePoint>> pointMap = baseDevicePoints.stream().collect(Collectors.groupingBy(BaseDevicePoint::getId));
                for (FireInfo fireInfo : fireInfos) {
                    WebsocketInfo websocketInfo = new WebsocketInfo();
                    websocketInfo.setType("fire");
                    websocketInfo.setBuildingId((String) param.get("buildingId"));
                    HashMap<String, Object> data = new HashMap<>();
                    List<BaseDevicePoint> baseDevicePointList = pointMap.get(fireInfo.getPointId());
//                    BaseDevicePoint baseDevicePoint = (BaseDevicePoint) baseDevicePoints1
                    FloorInfo floorInfo = new FloorInfo();
                    if (!CollectionUtils.isEmpty(baseDevicePointList)) {
                        BaseDevicePoint baseDevicePoint = baseDevicePointList.get(0);
                        floorInfo.setId(baseDevicePoint.getFloorId());
                        floorInfo.setFloor(baseDevicePoint.getFloor() == null ? 0 : Integer.parseInt(baseDevicePoint.getFloor() + ""));
                        floorInfo.setName(baseDevicePoint.getFloorName());
                        floorInfo.setPointX(baseDevicePoint.getWide() == null ? 0.0 : Double.valueOf(baseDevicePoint.getWide() + ""));
                        floorInfo.setPointY(baseDevicePoint.getTall() == null ? 0.0 : Double.valueOf(baseDevicePoint.getTall() + ""));
                    }
                    data.put("floorInfo", floorInfo);
                    data.put("alarmInfo", fireInfo);
                    websocketInfo.setData(data);
                    result.add(websocketInfo);
                }
            }
            //火警
//            for (FireInfo fireInfo : fireInfos) {
//                WebsocketInfo websocketInfo = new WebsocketInfo();
//                websocketInfo.setType("fire");
//                websocketInfo.setBuildingId((String) param.get("buildingId"));
//                HashMap<String, Object> data = new HashMap<>();
//                BaseDevicePoint baseDevicePoint = baseDevicePointMapper.selectById(fireInfo.getPointId());
//                FloorInfo floorInfo = new FloorInfo();
//                if (baseDevicePoint != null) {
//                    floorInfo.setId(baseDevicePoint.getFloorId());
//                    floorInfo.setFloor(baseDevicePoint.getFloor() == null ? 0 : Integer.parseInt(baseDevicePoint.getFloor() + ""));
//                    floorInfo.setName(baseDevicePoint.getFloorName());
//                    floorInfo.setPointX(baseDevicePoint.getPointX() == null ? 0.0 : Double.valueOf(baseDevicePoint.getPointX() + ""));
//                    floorInfo.setPointY(baseDevicePoint.getPointY() == null ? 0.0 : Double.valueOf(baseDevicePoint.getPointY() + ""));
//                }
//                data.put("floorInfo", floorInfo);
//                data.put("alarmInfo", fireInfo);
//                websocketInfo.setData(data);
//                result.add(websocketInfo);
//            }
        }
        if (waringFireInfos != null && waringFireInfos.size() > 0) {
            //预警
            // 先获取所有的点位id
            List<String> pids = waringFireInfos.stream().map(FireInfo::getPointId).collect(Collectors.toList());
            List<BaseDevicePoint> baseDevicePoints = baseDevicePointMapper.selectBatchIds(pids);
            if (!CollectionUtils.isEmpty(baseDevicePoints)) {
                Map<String, List<BaseDevicePoint>> pointMap = baseDevicePoints.stream().collect(Collectors.groupingBy(BaseDevicePoint::getId));
                for (FireInfo waringFireInfo : waringFireInfos) {
                    WebsocketInfo websocketInfo = new WebsocketInfo();
                    websocketInfo.setType("warning");
                    websocketInfo.setBuildingId((String) param.get("buildingId"));
                    HashMap<String, Object> data = new HashMap<>();
//                    BaseDevicePoint baseDevicePoint = baseDevicePointMapper.selectById(waringFireInfo.getPointId());
                    List<BaseDevicePoint> baseDevicePointList = pointMap.get(waringFireInfo.getPointId());
//                    BaseDevicePoint baseDevicePoint = (BaseDevicePoint) baseDevicePoints1
                    FloorInfo floorInfo = new FloorInfo();
                    if (!CollectionUtils.isEmpty(baseDevicePointList)) {
                        BaseDevicePoint baseDevicePoint = baseDevicePointList.get(0);
                        floorInfo.setId(baseDevicePoint.getFloorId());
                        floorInfo.setFloor(baseDevicePoint.getFloor() == null ? 0 : Integer.parseInt(baseDevicePoint.getFloor() + ""));
                        floorInfo.setName(baseDevicePoint.getFloorName());
                        floorInfo.setPointX(baseDevicePoint.getWide() == null ? 0.0 : Double.valueOf(baseDevicePoint.getWide() + ""));
                        floorInfo.setPointY(baseDevicePoint.getTall() == null ? 0.0 : Double.valueOf(baseDevicePoint.getTall() + ""));
                    }
                    data.put("floorInfo", floorInfo);
                    data.put("alarmInfo", waringFireInfo);
                    websocketInfo.setData(data);
                    result.add(websocketInfo);
                }
            }
//            for (FireInfo waringFireInfo : waringFireInfos) {
//                WebsocketInfo websocketInfo = new WebsocketInfo();
//                websocketInfo.setType("warning");
//                websocketInfo.setBuildingId((String) param.get("buildingId"));
//                HashMap<String, Object> data = new HashMap<>();
//                BaseDevicePoint baseDevicePoint = baseDevicePointMapper.selectById(waringFireInfo.getPointId());
//                FloorInfo floorInfo = new FloorInfo();
//                if (baseDevicePoint != null) {
//                    floorInfo.setId(baseDevicePoint.getFloorId());
//                    floorInfo.setFloor(baseDevicePoint.getFloor() == null ? 0 : Integer.parseInt(baseDevicePoint.getFloor() + ""));
//                    floorInfo.setName(baseDevicePoint.getFloorName());
//                    floorInfo.setPointX(baseDevicePoint.getPointX() == null ? 0.0 : Double.valueOf(baseDevicePoint.getPointX() + ""));
//                    floorInfo.setPointY(baseDevicePoint.getPointY() == null ? 0.0 : Double.valueOf(baseDevicePoint.getPointY() + ""));
//                }
//                data.put("floorInfo", floorInfo);
//                data.put("alarmInfo", waringFireInfo);
//                websocketInfo.setData(data);
//                result.add(websocketInfo);
//            }
        }
        if (faultInfos != null && faultInfos.size() > 0) {
            //故障
            // 先获取所有的点位id
            List<String> pids = Lists.newArrayList();
            faultInfos.forEach(map -> {
                pids.add((String) map.get("pointId"));
            });
            List<BaseDevicePoint> baseDevicePoints = baseDevicePointMapper.selectBatchIds(pids);
            if (!CollectionUtils.isEmpty(baseDevicePoints)) {
                Map<String, List<BaseDevicePoint>> pointMap = baseDevicePoints.stream().collect(Collectors.groupingBy(BaseDevicePoint::getId));
                for (Map<String, Object> faultInfo : faultInfos) {
                    WebsocketInfo websocketInfo = new WebsocketInfo();
                    websocketInfo.setType("fault");
                    websocketInfo.setBuildingId((String) param.get("buildingId"));
                    HashMap<String, Object> data = new HashMap<>();
                    List<BaseDevicePoint> baseDevicePointList = pointMap.get((String) faultInfo.get("pointId"));
//                    BaseDevicePoint baseDevicePoint = baseDevicePointMapper.selectById((String) faultInfo.get("pointId"));
                    FloorInfo floorInfo = new FloorInfo();
                    if (!CollectionUtils.isEmpty(baseDevicePointList)) {
                        BaseDevicePoint baseDevicePoint = baseDevicePointList.get(0);
                        floorInfo.setId(baseDevicePoint.getFloorId());
                        floorInfo.setFloor(baseDevicePoint.getFloor() == null ? 0 : Integer.parseInt(baseDevicePoint.getFloor() + ""));
                        floorInfo.setName(baseDevicePoint.getFloorName());
                        floorInfo.setPointX(baseDevicePoint.getWide() == null ? 0.0 : Double.valueOf(baseDevicePoint.getWide() + ""));
                        floorInfo.setPointY(baseDevicePoint.getTall() == null ? 0.0 : Double.valueOf(baseDevicePoint.getTall() + ""));
                    }
                    data.put("floorInfo", floorInfo);
                    data.put("alarmInfo", faultInfo);
                    websocketInfo.setData(data);
                    result.add(websocketInfo);
                }
            }
//            for (Map<String, Object> faultInfo : faultInfos) {
//                WebsocketInfo websocketInfo = new WebsocketInfo();
//                websocketInfo.setType("fault");
//                websocketInfo.setBuildingId((String) param.get("buildingId"));
//                HashMap<String, Object> data = new HashMap<>();
//                BaseDevicePoint baseDevicePoint = baseDevicePointMapper.selectById((String) faultInfo.get("pointId"));
//                FloorInfo floorInfo = new FloorInfo();
//                if (baseDevicePoint != null) {
//                    floorInfo.setId(baseDevicePoint.getFloorId());
//                    floorInfo.setFloor(baseDevicePoint.getFloor() == null ? 0 : Integer.parseInt(baseDevicePoint.getFloor() + ""));
//                    floorInfo.setName(baseDevicePoint.getFloorName());
//                    floorInfo.setPointX(baseDevicePoint.getPointX() == null ? 0.0 : Double.valueOf(baseDevicePoint.getPointX() + ""));
//                    floorInfo.setPointY(baseDevicePoint.getPointY() == null ? 0.0 : Double.valueOf(baseDevicePoint.getPointY() + ""));
//                }
//                data.put("floorInfo", floorInfo);
//                data.put("alarmInfo", faultInfo);
//                websocketInfo.setData(data);
//                result.add(websocketInfo);
//            }
        }
        if (statPumpExpections != null && statPumpExpections.size() > 0) {
            //水泵
            // 先获取所有的点位id
            List<String> pids = Lists.newArrayList();
            statPumpExpections.forEach(map -> {
                pids.add((String) map.get("pointId"));
            });
            List<BaseDevicePoint> baseDevicePoints = baseDevicePointMapper.selectBatchIds(pids);
            if (!CollectionUtils.isEmpty(baseDevicePoints)) {
                Map<String, List<BaseDevicePoint>> pointMap = baseDevicePoints.stream().collect(Collectors.groupingBy(BaseDevicePoint::getId));
                for (Map<String, Object> statPump : statPumpExpections) {
                    WebsocketInfo websocketInfo = new WebsocketInfo();
                    websocketInfo.setType("pump-error");
                    websocketInfo.setBuildingId((String) param.get("buildingId"));
                    HashMap<String, Object> data = new HashMap<>();
//                    BaseDevicePoint baseDevicePoint = baseDevicePointMapper.selectById((String) statPump.get("pointId"));
                    List<BaseDevicePoint> baseDevicePointList = pointMap.get((String) statPump.get("pointId"));
//                    BaseDevicePoint baseDevicePoint = baseDevicePointMapper.selectById((String) faultInfo.get("pointId"));
                    FloorInfo floorInfo = new FloorInfo();
                    if (!CollectionUtils.isEmpty(baseDevicePointList)) {
                        BaseDevicePoint baseDevicePoint = baseDevicePointList.get(0);
                        floorInfo.setId(baseDevicePoint.getFloorId());
                        floorInfo.setFloor(baseDevicePoint.getFloor() == null ? 0 : Integer.parseInt(baseDevicePoint.getFloor() + ""));
                        floorInfo.setName(baseDevicePoint.getFloorName());
                        floorInfo.setPointX(baseDevicePoint.getWide() == null ? 0.0 : Double.valueOf(baseDevicePoint.getWide() + ""));
                        floorInfo.setPointY(baseDevicePoint.getTall() == null ? 0.0 : Double.valueOf(baseDevicePoint.getTall() + ""));
                    }
                    data.put("floorInfo", floorInfo);
                    data.put("alarmInfo", statPump);
                    websocketInfo.setData(data);
                    result.add(websocketInfo);
                }

            }
//            for (Map<String, Object> statPump : statPumpExpections) {
//                WebsocketInfo websocketInfo = new WebsocketInfo();
//                websocketInfo.setType("pump-error");
//                websocketInfo.setBuildingId((String) param.get("buildingId"));
//                HashMap<String, Object> data = new HashMap<>();
//                BaseDevicePoint baseDevicePoint = baseDevicePointMapper.selectById((String) statPump.get("pointId"));
//                FloorInfo floorInfo = new FloorInfo();
//                if (baseDevicePoint != null) {
//                    floorInfo.setId(baseDevicePoint.getFloorId());
//                    floorInfo.setFloor(baseDevicePoint.getFloor() == null ? 0 : Integer.parseInt(baseDevicePoint.getFloor() + ""));
//                    floorInfo.setName(baseDevicePoint.getFloorName());
//                    floorInfo.setPointX(baseDevicePoint.getPointX() == null ? 0.0 : Double.valueOf(baseDevicePoint.getPointX() + ""));
//                    floorInfo.setPointY(baseDevicePoint.getPointY() == null ? 0.0 : Double.valueOf(baseDevicePoint.getPointY() + ""));
//                }
//                data.put("floorInfo", floorInfo);
//                data.put("alarmInfo", statPump);
//                websocketInfo.setData(data);
//                result.add(websocketInfo);
//            }
        }
        if (StatWaterExpections != null && StatWaterExpections.size() > 0) {
            //水压
            // 先获取所有的点位id
            List<String> pids = Lists.newArrayList();
            StatWaterExpections.forEach(map -> {
                pids.add((String) map.get("pointId"));
            });
            List<BaseDevicePoint> baseDevicePoints = baseDevicePointMapper.selectBatchIds(pids);
            if (!CollectionUtils.isEmpty(baseDevicePoints)) {
                Map<String, List<BaseDevicePoint>> pointMap = baseDevicePoints.stream().collect(Collectors.groupingBy(BaseDevicePoint::getId));
                for (Map<String, Object> statWater : StatWaterExpections) {
                    WebsocketInfo websocketInfo = new WebsocketInfo();
                    websocketInfo.setType("water-error");
                    websocketInfo.setBuildingId((String) param.get("buildingId"));
                    HashMap<String, Object> data = new HashMap<>();
//                    BaseDevicePoint baseDevicePoint = baseDevicePointMapper.selectById((String) statWater.get("pointId"));
                    List<BaseDevicePoint> baseDevicePointList = pointMap.get((String) statWater.get("pointId"));
                    FloorInfo floorInfo = new FloorInfo();
                    if (!CollectionUtils.isEmpty(baseDevicePointList)) {
                        BaseDevicePoint baseDevicePoint = baseDevicePointList.get(0);
                        floorInfo.setId(baseDevicePoint.getFloorId());
                        floorInfo.setFloor(baseDevicePoint.getFloor() == null ? 0 : Integer.parseInt(baseDevicePoint.getFloor() + ""));
                        floorInfo.setName(baseDevicePoint.getFloorName());
                        floorInfo.setPointX(baseDevicePoint.getWide() == null ? 0.0 : Double.valueOf(baseDevicePoint.getWide() + ""));
                        floorInfo.setPointY(baseDevicePoint.getTall() == null ? 0.0 : Double.valueOf(baseDevicePoint.getTall() + ""));
                    }
                    data.put("floorInfo", floorInfo);
                    data.put("alarmInfo", statWater);
                    websocketInfo.setData(data);
                    result.add(websocketInfo);
                }
            }
//            for (Map<String, Object> statWater : StatWaterExpections) {
//                WebsocketInfo websocketInfo = new WebsocketInfo();
//                websocketInfo.setType("water-error");
//                websocketInfo.setBuildingId((String) param.get("buildingId"));
//                HashMap<String, Object> data = new HashMap<>();
//                BaseDevicePoint baseDevicePoint = baseDevicePointMapper.selectById((String) statWater.get("pointId"));
//                FloorInfo floorInfo = new FloorInfo();
//                if (baseDevicePoint != null) {
//                    floorInfo.setId(baseDevicePoint.getFloorId());
//                    floorInfo.setFloor(baseDevicePoint.getFloor() == null ? 0 : Integer.parseInt(baseDevicePoint.getFloor() + ""));
//                    floorInfo.setName(baseDevicePoint.getFloorName());
//                    floorInfo.setPointX(baseDevicePoint.getPointX() == null ? 0.0 : Double.valueOf(baseDevicePoint.getPointX() + ""));
//                    floorInfo.setPointY(baseDevicePoint.getPointY() == null ? 0.0 : Double.valueOf(baseDevicePoint.getPointY() + ""));
//                }
//                data.put("floorInfo", floorInfo);
//                data.put("alarmInfo", statWater);
//                websocketInfo.setData(data);
//                result.add(websocketInfo);
//            }
        }

        return result;
    }

    //public void addToFloorPlanList(String type,String buildingId);

    /**
     * 火警待处理，待填报，超时处理，超时填报条数统计
     *
     * @param request
     * @return
     */
    @Override
    public Map<String, Integer> getFireStatisticInfo(HttpServletRequest request) {

        Map<String, Object> param = ServletsUtil.getParameters(request);
        //参数添加起止时间 param.startTime param.endTime
        paramAddDate(param);
        //今日未处理火警
        //List<FireInfo> fireInfos = fireInfoMapper.selectFireExpection(param);
        //历史所有火警
        param.put("type", "0");
        List<FireInfo> fireInfoAll = fireInfoMapper.selectFireExpectionAll(param);
        Map<String, Integer> result = new HashMap<>();

        //查询配置表处理超时时间
        param.put("config_code", "dxtz_1_cssz");
        List<Map<String, Object>> configRelation = configRelationMapper.selectByConfigCode("dxtz_1_cssz");
        Double delWithTime = Double.valueOf(configRelation.get(0).get(ConfigRelationUtil.CON_INT_MIN) + "");

        //等待处理
        int waitProCount = 0;
        //处理超时 今日超时数据仍然记在其内
        int waitProOutCount = 0;
        //等待填报
        int fillInCount = 0;
        //填报超时 今日超时数据仍然记在其内
        int fillInOutCount = 0;

        if (fireInfoAll != null && fireInfoAll.size() > 0) {
            for (FireInfo fireInfo : fireInfoAll) {
                String executeTime = fireInfo.getExecuteTime();
                String firstTime = fireInfo.getFirstTime();
                String fillInTime = fireInfo.getFillInTime();
                String lastTime = fireInfo.getLastTime();
                //fillInTime = fillInTime.substring(0,fillInTime.lastIndexOf("."));
                //今日数据:已处理的超时火警仍然记在今日超时中
                if (StringUtils.equals(fireInfo.getFireStatus(), "0") && (StringUtils.equals(fireInfo.getReportStatus(), "0"))) {
                    //未处理
                    waitProCount++;

                    //没有处理时间说明还未处理
                    int min = DateUtils.minusMinute(new Date(), DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss"));

                    //超时的只查当日的
                    if (min > delWithTime && (DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss").getTime() > DateUtil.parseDate(param.get("startTime").toString(), "yyyy-MM-dd HH:mm:ss").getTime())) {
                        waitProOutCount++;
                    }
                } else {
                    //超时的只查当日的
                    int min = DateUtils.minusMinute(DateUtil.parseDate(executeTime, "yyyy-MM-dd HH:mm:ss"), DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss"));
                    if (min > delWithTime && (DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss").getTime() > DateUtil.parseDate(param.get("startTime").toString(), "yyyy-MM-dd HH:mm:ss").getTime())) {
                        waitProOutCount++;
                    }
                }

//                if (StringUtils.equals(fireInfo.getFireStatus(), "0") && (StringUtils.equals(fireInfo.getReportStatus(), "0") || StringUtils.isEmpty(fireInfo.getReportStatus()))) {
//                    //未处理
//                    waitProCount++;
//                }
                if (StringUtils.equals(fireInfo.getFireStatus(), "1") && StringUtils.equals(fireInfo.getReportStatus(), "0")) {
                    //已处理，等待填报
                    fillInCount++;
                }
                if (StringUtils.equals(fireInfo.getFireStatus(), "1") && StringUtils.equals(fireInfo.getReportStatus(), "0") && StringUtils.isBlank(fillInTime) && StringUtils.isNotEmpty(executeTime)) {
                    //未完成填报的填报超时
                    int min = DateUtils.minusMinute(new Date(), DateUtil.parseDate(executeTime, "yyyy-MM-dd HH:mm:ss"));
                    //超时的只查当日的
                    if (min > 30 && (DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss").getTime() > DateUtil.parseDate(param.get("startTime").toString(), "yyyy-MM-dd HH:mm:ss").getTime())) {
                        fillInOutCount++;
                    }
                }
                if (StringUtils.equals(fireInfo.getFireStatus(), "1") && StringUtils.equals(fireInfo.getReportStatus(), "1") && StringUtils.isNotEmpty(fillInTime) && StringUtils.isNotEmpty(executeTime)) {
                    //已完成的填报的填报超时
                    int min = DateUtils.minusMinute(DateUtil.parseDate(fillInTime, "yyyy-MM-dd HH:mm:ss"), DateUtil.parseDate(executeTime, "yyyy-MM-dd HH:mm:ss"));
                    //超时的只查当日的
                    if (min > 30 && (DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss").getTime() > DateUtil.parseDate(param.get("startTime").toString(), "yyyy-MM-dd HH:mm:ss").getTime())) {
                        fillInOutCount++;
                    }
                }
            }
        }
        result.put("waitProCount", waitProCount);
        result.put("waitProOutCount", waitProOutCount);
        result.put("fillInCount", fillInCount);
        result.put("fillInOutCount", fillInOutCount);

        return result;
    }

    /**
     * 火警处理超时，填报超时列表
     *
     * @param request
     * @return
     */
    @Override
    public TablePageData getOutTimeInfo(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        int pageIndex = Integer.parseInt(param.get("pageIndex") + "");
        int pageRows = Integer.parseInt(param.get("pageRows") + "");
        //Page<?> page = PageParam.initPagination(Integer.parseInt(param.get("pageIndex")+""), Integer.parseInt(param.get("pageRows")+""));
        //参数添加起止时间
        paramAddDate(param);
        String type = (String) param.get("type");
        //今日所有火警
        param.put("infoType", "0");
        //只查今日的超时数据
        List<Map<String, Object>> fireInfoToday = fireInfoMapper.selectTodayFireInfo(param);
        //今日之前
        //List<Map<String, Object>> fireInfoBefore = fireInfoMapper.selectBeforeFireInfo(param);
        //并集
        //fireInfoToday.addAll(fireInfoBefore);

        List<Map<String, Object>> fireInfos = new ArrayList<>();
        //查询配置表处理超时时间
        List<Map<String, Object>> configRelation = configRelationMapper.selectByConfigCode("dxtz_1_cssz");
        Double delWithTime = Double.valueOf(configRelation.get(0).get(ConfigRelationUtil.CON_INT_MIN) + "");
        if (StringUtils.equals(type, "0")) {
            //处理超时的弹窗
            //fireInfos = fireInfoMapper.selectWaitOutTime(param);
            for (Map<String, Object> fireInfo : fireInfoToday) {
                String executeTime = (String) fireInfo.get("executeTime");
                String firstTime = (String) fireInfo.get("firstTime");
                String fireStatus = (String) fireInfo.get("fireStatus");
                String reportStatus = (String) fireInfo.get("reportStatus");
                if (StringUtils.equals(fireStatus, "0") && (StringUtils.equals(reportStatus, "0") && StringUtils.isBlank(executeTime))) {
                    //没有处理时间说明还未处理
                    int min = DateUtils.minusMinute(new Date(), DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss"));
                    if (min > delWithTime) {
                        fireInfos.add(fireInfo);
                    }
                } else {
                    int min = DateUtils.minusMinute(DateUtil.parseDate(executeTime, "yyyy-MM-dd HH:mm:ss"), DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss"));
                    if (min > delWithTime) {
                        fireInfos.add(fireInfo);
                    }
                }
            }
        } else {
            //填报超时的弹窗
            //fireInfos = fireInfoMapper.selectFillInOutTime(param);
            for (Map<String, Object> fireInfo : fireInfoToday) {
                String executeTime = (String) fireInfo.get("executeTime");
                String fillInTime = (String) fireInfo.get("fillInTime");
                String fireStatus = (String) fireInfo.get("fireStatus");
                String reportStatus = (String) fireInfo.get("reportStatus");
                if (StringUtils.equals(fireStatus, "1") && StringUtils.equals(reportStatus, "0") && StringUtils.isBlank(fillInTime) && StringUtils.isNotEmpty(executeTime)) {
                    //未完成填报的填报超时
                    int min = DateUtils.minusMinute(new Date(), DateUtil.parseDate(executeTime, "yyyy-MM-dd HH:mm:ss"));
                    if (min > delWithTime) {
                        fireInfos.add(fireInfo);
                    }
                }
                if (StringUtils.equals(fireStatus, "1") && StringUtils.equals(reportStatus, "1") && StringUtils.isNotEmpty(fillInTime) && StringUtils.isNotEmpty(executeTime)) {
                    //已完成的填报的填报超时
                    int min = DateUtils.minusMinute(DateUtil.parseDate(fillInTime, "yyyy-MM-dd HH:mm:ss"), DateUtil.parseDate(executeTime, "yyyy-MM-dd HH:mm:ss"));
                    if (min > delWithTime) {
                        fireInfos.add(fireInfo);
                    }
                }
            }
        }
        TablePageData tablePageData = new TablePageData();
        PageParam pageParam = PageParam.getPage(pageIndex, pageRows);
        List<Map<String, Object>> result = new ArrayList<>();
        int totalSize = 0;
        if (fireInfos.size() < pageIndex * pageRows) {
            totalSize = fireInfos.size();
        } else {
            totalSize = pageIndex * pageRows;
        }
        for (int i = (pageIndex - 1) * pageRows; i < totalSize; i++) {
            result.add(fireInfos.get(i));
        }
        pageParam.setTotalRows(fireInfos.size());
        tablePageData.setPageParam(pageParam);
        tablePageData.setTableData(result);
        return tablePageData;
    }

    @Override
    public TablePageData getFloorExpectionInfo(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        List<Map<String, Object>> resultList = new ArrayList<>();
        int pageIndex = Integer.parseInt(param.get("pageIndex") + "");
        int pageRows = Integer.parseInt(param.get("pageRows") + "");
        //参数添加起止时间
        paramAddDate(param);
        /*List<Map<String,Object>> fireInfos = firePageWindowMapper.selectFloorFireInf(param);
        for(Map<String,Object> map : fireInfos){
            //查询7天内上报次数
            int num = fireInfoMapper.selectCount(new QueryWrapper<FireInfo>().eq("building_id",param.get("buildingId")).eq("point_id",map.get("pointId"))
                    .ge("first_time",getPastDate(7,"yyyy-MM-dd HH:mm:ss")).le("first_time",DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss")));
            map.put("num",num+"");
            Map<String,Object> fireCheckInfo = getFireCheckInfo(String.valueOf(map.get("id")));
            map.putAll(fireCheckInfo);
            resultList.add(map);
        }*/

        List<Map<String, Object>> StatWaterExpections = firePageWindowMapper.selectWaterExpection(param);
        List<Map<String, Object>> statPumpExpections = firePageWindowMapper.selectPumpExpection(param);
        resultList.addAll(StatWaterExpections);
        resultList.addAll(statPumpExpections);

        //分页
        TablePageData tablePageData = new TablePageData();
        PageParam pageParam = PageParam.getPage(pageIndex, pageRows);
        List<Map<String, Object>> result = new ArrayList<>();
        int totalSize = 0;
        if (resultList.size() < pageIndex * pageRows) {
            totalSize = resultList.size();
        } else {
            totalSize = pageIndex * pageRows;
        }
        for (int i = (pageIndex - 1) * pageRows; i < totalSize; i++) {
            result.add(resultList.get(i));
        }
        pageParam.setTotalRows(resultList.size());
        tablePageData.setPageParam(pageParam);
        tablePageData.setTableData(result);

        return tablePageData;
    }

    /**
     * 预警待处理查询弹窗
     *
     * @param request
     * @return
     */
    @Override
    public TablePageData getWaringInfoWindow(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        List<Map<String, Object>> resultList = new ArrayList<>();
        int pageIndex = Integer.parseInt(param.get("pageIndex") + "");
        int pageRows = Integer.parseInt(param.get("pageRows") + "");
        Page<?> page = PageParam.initPagination(pageIndex, pageRows);
        //参数添加起止时间
        paramAddDate(param);
        //查询预警信息 (今日)
        param.put("type", "6");
        //List<Map<String, Object>> fireInfoList = firePageWindowMapper.getWindowInfoListByPage(page, param);
        /*for (Map<String, Object> map : fireInfoList) {
            //查询上报次数
            int num = 0;
            FarEastoneCache farEastoneCache = FastJSONUtils.toBean(redisUtils.get("point_status:"+map.get("buildingId") + "-" + map.get("pointId")) +"", FarEastoneCache.class);
            if(farEastoneCache!=null){
                num = farEastoneCache.getFireReportTimes();
            }
            map.put("num", map.get("num"));
            Map<String, Object> fireCheckInfo = getFireCheckInfo(String.valueOf(map.get("id")));
            map.putAll(fireCheckInfo);
            resultList.add(map);
        }*/

        //查询预警信息 (今日 + 今日之前)
        BaseBuilding baseBuilding = baseBuildingMapper.selectById(param.get("buildingId").toString());
        List<Map<String, Object>> fireInfoBeforeList = firePageWindowMapper.getWindowInfoBeforeListByPage(page, param);
        for (Map<String, Object> map : fireInfoBeforeList) {
            //查询上报次数
//            int num = 0;
//            FarEastoneCache farEastoneCache = FastJSONUtils.toBean(redisUtils.get("point_status:" + map.get("buildingId") + "-" + map.get("pointId")) + "", FarEastoneCache.class);
//            if (farEastoneCache != null) {
//                num = farEastoneCache.getFireReportTimes();
//            }
            //map.put("num", map.get("num"));
            map.put("buildingName", baseBuilding.getBuildingName());
            map.put("ctrlPhone", baseBuilding.getCtrlPhone());
            map.put("centerPhone", baseBuilding.getCenterPhone());
            Map<String, Object> fireCheckInfo = getFireCheckInfo(String.valueOf(map.get("id")));
            map.putAll(fireCheckInfo);
            resultList.add(map);
        }

        TablePageData result = new TablePageData();
        //获取完整分页信息
        PageParam pageParam = PageParam.getPage(Integer.parseInt(param.get("pageIndex") + ""), Integer.parseInt(param.get("pageRows") + ""));
        pageParam.setTotalRows(page.getTotal());
        result.setPageParam(pageParam);
        result.setTableData(resultList);
        return result;
    }

    /**
     * 获取测试单位名单
     *
     * @param request
     * @return
     */
    @Override
    public List<Map<String, String>> getTestUnitName(HttpServletRequest request) {
        List<Map<String, String>> testUnits = new ArrayList<>();
        Map<String, String> map1 = new HashMap<>();
        map1.put("fullName", "远程监督中心");
        map1.put("userId", "远程监督中心");
        Map<String, String> map2 = new HashMap<>();
        map2.put("fullName", "安监中心");
        map2.put("userId", "安监中心");
        Map<String, String> map3 = new HashMap<>();
        map3.put("fullName", "运营中心");
        map3.put("userId", "运营中心");
        Map<String, String> map7 = new HashMap<>();
        map7.put("fullName", "包头市智慧消防中心");
        map7.put("userId", "包头市智慧消防中心");
        Map<String, String> map4 = new HashMap<>();
        map4.put("fullName", "区域");
        map4.put("userId", "区域");
        Map<String, String> map5 = new HashMap<>();
        map5.put("fullName", "广场");
        map5.put("userId", "广场");
        Map<String, String> map6 = new HashMap<>();
        map6.put("fullName", "其他");
        map6.put("userId", "其他");

        testUnits.add(map1);
        testUnits.add(map2);
        testUnits.add(map3);
        testUnits.add(map7);
        testUnits.add(map4);
        testUnits.add(map5);
        testUnits.add(map6);
        return testUnits;
    }

    /**
     * 预警数据统计
     *
     * @param request
     * @return
     */
    @Override
    public Map<String, Integer> getWaringStatisticInfo(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        paramAddDate(param);
        //今日所有火警
        param.put("type", "6");
        //List<FireInfo> fireInfoAll = fireInfoMapper.selectFireExpectionAll(param);
        Map<String, Integer> result = new HashMap<>();
        //预警等待处理
        int warningCount = 0;
        //预警处理超时
        int warningOutCount = 0;

        //查询配置表处理超时时间
        List<Map<String, Object>> configRelation = configRelationMapper.selectByConfigCode("dxtz_2_cssz");
        Double delWithTime = Double.valueOf(configRelation.get(0).get(ConfigRelationUtil.CON_INT_MIN) + "");

        /*if (fireInfoAll != null && fireInfoAll.size() > 0) {

            for (FireInfo fireInfo : fireInfoAll) {
                String executeTime = fireInfo.getExecuteTime();
                String firstTime = fireInfo.getFirstTime();
                String fillInTime = fireInfo.getFillInTime();
                //fillInTime = fillInTime.substring(0,fillInTime.lastIndexOf("."));
                if (!StringUtils.equals(fireInfo.getFireStatus(), "1") && StringUtils.isBlank(executeTime)) {
                    //没有处理时间说明还未处理
                    int min = DateUtils.minusMinute(new Date(), DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss"));
                    if (min > delWithTime) {
                        warningOutCount++;
                    }
                } else {
                    int min = DateUtils.minusMinute(DateUtil.parseDate(executeTime, "yyyy-MM-dd HH:mm:ss"), DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss"));
                    if (min > delWithTime) {
                        warningOutCount++;
                    }
                }

                if (StringUtils.equals(fireInfo.getFireStatus(), "0")) {
                    //未处理
                    warningCount++;
                }
            }
        }*/

        List<FireInfo> fireInfoAll = fireInfoMapper.selectFireExpectionAllBefore(param);
        if (fireInfoAll != null && fireInfoAll.size() > 0) {

            for (FireInfo fireInfo : fireInfoAll) {
                String executeTime = fireInfo.getExecuteTime();
                String firstTime = fireInfo.getFirstTime();
                String fillInTime = fireInfo.getFillInTime();
                String lastTime = fireInfo.getLastTime();
                //fillInTime = fillInTime.substring(0,fillInTime.lastIndexOf("."));
                if (!StringUtils.equals(fireInfo.getFireStatus(), "1")) {
                    //没有处理时间说明还未处理
                    int min = DateUtils.minusMinute(new Date(), DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss"));
                    if (min > delWithTime && (DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss").getTime() > DateUtil.parseDate(param.get("startTime").toString(), "yyyy-MM-dd HH:mm:ss").getTime())) {
                        warningOutCount++;
                    }
                } else {
                    if (StringUtils.isNotEmpty(executeTime)) {
                        int min = DateUtils.minusMinute(DateUtil.parseDate(executeTime, "yyyy-MM-dd HH:mm:ss"), DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss"));
                        if (min > delWithTime && (DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss").getTime() > DateUtil.parseDate(param.get("startTime").toString(), "yyyy-MM-dd HH:mm:ss").getTime())) {
                            warningOutCount++;
                        }
                    }
                }

                if (StringUtils.equals(fireInfo.getFireStatus(), "0")) {
                    //未处理
                    warningCount++;
                }
            }
        }


        result.put("warningCount", warningCount);
        result.put("warningOutCount", warningOutCount);
        return result;
    }


    /**
     * 预警处理超时查询
     *
     * @param request
     * @return
     */
    @Override
    public TablePageData getWaringOutTimeInfo(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        int pageIndex = Integer.parseInt(param.get("pageIndex") + "");
        int pageRows = Integer.parseInt(param.get("pageRows") + "");
        //参数添加起止时间
        paramAddDate(param);
        //历史所有预警
        param.put("infoType", "6");
        List<Map<String, Object>> fireInfoToday = fireInfoMapper.selectTodayFireInfo(param);
        //所有火警 (所有)
        //List<Map<String, Object>> fireInfoBefore = fireInfoMapper.selectBeforeFireInfo(param);

        //查询配置表处理超时时间
        List<Map<String, Object>> configRelation = configRelationMapper.selectByConfigCode("dxtz_2_cssz");
        Double delWithTime = Double.valueOf(configRelation.get(0).get(ConfigRelationUtil.CON_INT_MIN) + "");
        List<Map<String, Object>> fireInfos = new ArrayList<>();
        if (fireInfoToday != null && fireInfoToday.size() > 0) {
            for (Map<String, Object> fireInfo : fireInfoToday) {
                String executeTime = (String) fireInfo.get("executeTime");
                String firstTime = (String) fireInfo.get("firstTime");
                String fireStatus = (String) fireInfo.get("fireStatus");
                String reportStatus = (String) fireInfo.get("reportStatus");
                if (!StringUtils.equals(fireStatus, "1")) {
                    //没有处理时间说明还未处理
                    int min = DateUtils.minusMinute(new Date(), DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss"));
                    if (min > delWithTime) {
                        fireInfos.add(fireInfo);
                    }
                } else {
                    if (StringUtils.isNotEmpty(executeTime)) {
                        int min = DateUtils.minusMinute(DateUtil.parseDate(executeTime, "yyyy-MM-dd HH:mm:ss"), DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss"));
                        if (min > delWithTime) {
                            fireInfos.add(fireInfo);
                        }
                    }
                }
            }
        }


        TablePageData tablePageData = new TablePageData();
        PageParam pageParam = PageParam.getPage(pageIndex, pageRows);
        List<Map<String, Object>> result = new ArrayList<>();
        int totalSize = 0;
        if (fireInfos.size() < pageIndex * pageRows) {
            totalSize = fireInfos.size();
        } else {
            totalSize = pageIndex * pageRows;
        }
        for (int i = (pageIndex - 1) * pageRows; i < totalSize; i++) {
            result.add(fireInfos.get(i));
        }
        pageParam.setTotalRows(fireInfos.size());
        tablePageData.setPageParam(pageParam);
        tablePageData.setTableData(result);
        return tablePageData;
    }

    /**
     * 故障待处理/处理中弹窗
     *
     * @param request
     * @return
     */
    @Override
    public TablePageData getFaultInfoWindow(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        //参数添加起止时间
        paramAddDate(param);
        List<Map<String, Object>> resultList = new ArrayList<>();
        int pageIndex = Integer.parseInt(param.get("pageIndex") + "");
        int pageRows = Integer.parseInt(param.get("pageRows") + "");
        Page<?> page = PageParam.initPagination(pageIndex, pageRows);
        /*List<Map<String, Object>> faultInfos = firePageWindowMapper.selectFaultProcessInfoByPage(page, param);
        if (faultInfos == null) {
            return null;
        }*/
        List<Map<String, Object>> faultInfoAll = firePageWindowMapper.selectFaultProcessAllInfoByPage(page, param);
        if (faultInfoAll == null) {
            return null;
        }
        /*for (Map<String, Object> stringObjectMap : faultInfos) {
            Map<String,Object> fireCheckInfo = getFireCheckInfo(String.valueOf(stringObjectMap.get("id")));
            stringObjectMap.putAll(fireCheckInfo);
            resultList.add(stringObjectMap);
        }*/
        TablePageData result = new TablePageData();
        //获取完整分页信息
        PageParam pageParam = PageParam.getPage(Integer.parseInt(param.get("pageIndex") + ""), Integer.parseInt(param.get("pageRows") + ""));
        pageParam.setTotalRows(page.getTotal());
        result.setPageParam(pageParam);
        //result.setTableData(faultInfos);
        result.setTableData(faultInfoAll);
        return result;
    }

    /**
     * 故障处理超时弹窗(超时只查今日的)
     *
     * @param
     * @return
     */
    @Override
    public TablePageData getFaultOutTimeWindow(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        //参数添加起止时间
        paramAddDate(param);
        int pageIndex = Integer.parseInt(param.get("pageIndex") + "");
        int pageRows = Integer.parseInt(param.get("pageRows") + "");
        List<Map<String, Object>> faultInfos = firePageWindowMapper.selectTodayFaultInfo(param); //今日
        //List<Map<String, Object>> faultInfos = firePageWindowMapper.selectAllFaultInfo(param); //今日 + 今日之前

        //查询配置表处理超时时间
        //TODO:查询配置库
        //List<Map<String,Object>> configRelation = configRelationMapper.selectByConfigCode("dxtz_2_cssz");
        //Double delWithTime = Double.valueOf(configRelation.get(0).get(ConfigRelationUtil.CON_INT_MIN)+"");

        if (faultInfos == null || faultInfos.size() == 0) {
            return null;
        }
        List<Map<String, Object>> realList = new ArrayList<>();
        for (Map<String, Object> faultInfo : faultInfos) {
            String faultStatus = (String) faultInfo.get("faultStatus");
            String firstTime = (String) faultInfo.get("firstTime");
            String handleTime = (String) faultInfo.get("handleTime");
            if (!StringUtils.equals(faultStatus, "已处理")) {
                //比较小时
                int hour = DateUtils.minusHour(new Date(), DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss"));
                if (hour > 0.5) {
                    realList.add(faultInfo);
                }
            } else {
                int hour = DateUtils.minusHour(DateUtil.parseDate(handleTime, "yyyy-MM-dd HH:mm:ss"), DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss"));
                if (hour > 0.5) {
                    realList.add(faultInfo);
                }
            }
        }

        //分页
        TablePageData tablePageData = new TablePageData();
        PageParam pageParam = PageParam.getPage(pageIndex, pageRows);
        List<Map<String, Object>> result = new ArrayList<>();
        int totalSize = 0;
        if (realList.size() < pageIndex * pageRows) {
            totalSize = realList.size();
        } else {
            totalSize = pageIndex * pageRows;
        }
        for (int i = (pageIndex - 1) * pageRows; i < totalSize; i++) {
            result.add(realList.get(i));
        }
        pageParam.setTotalRows(realList.size());
        tablePageData.setPageParam(pageParam);
        tablePageData.setTableData(result);
        return tablePageData;
    }

    /**
     * 故障统计条数信息
     *
     * @param request
     * @return
     */
    @Override
    public Map<String, Integer> getFaultStatisticInfo(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        //参数添加起止时间
        paramAddDate(param);
        //List<Map<String, Object>> faultInfos = firePageWindowMapper.selectTodayFaultInfo(param);//今日
        List<Map<String, Object>> faultInfos = firePageWindowMapper.selectAllFaultInfo(param);//今日 + 今日之前

        //故障-等待处理
        int faultCount = 0;
        //故障-正在处理
        int faultFillInCoutn = 0;
        //故障-处理超时
        int faultOutTime = 0;
        if (faultInfos != null && faultInfos.size() > 0) {
            for (Map<String, Object> faultInfo : faultInfos) {
                String faultStatus = (String) faultInfo.get("faultStatus");
                String firstTime = (String) faultInfo.get("firstTime");
                if (!StringUtils.equals(faultStatus, "已处理")) {
                    //比较小时
                    int hour = DateUtils.minusHour(new Date(), DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss"));
                    //TODO：查询配置表
                    if (hour > 0.5 && (DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss").getTime() > DateUtil.parseDate(param.get("startTime").toString(), "yyyy-MM-dd HH:mm:ss").getTime())) {
                        faultOutTime++;
                    }
                    if (StringUtils.equals(faultStatus, "未处理")) {
                        //未处理
                        faultCount++;
                    }
                    if (StringUtils.equals(faultStatus, "处理中")) {
                        //处理中
                        faultFillInCoutn++;
                    }
                } else {
                    String handleTime = (String) faultInfo.get("handleTime");
                    //已处理
                    int hour = DateUtils.minusHour(DateUtil.parseDate(handleTime, "yyyy-MM-dd HH:mm:ss"), DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss"));
                    if (hour > 0.5 && (DateUtil.parseDate(firstTime, "yyyy-MM-dd HH:mm:ss").getTime() > DateUtil.parseDate(param.get("startTime").toString(), "yyyy-MM-dd HH:mm:ss").getTime())) {

                        faultOutTime++;
                    }
                }
            }
        }
        Map<String, Integer> result = new HashMap<>();
        result.put("faultCount", faultCount);
        result.put("faultFillInCoutn", faultFillInCoutn);
        result.put("faultOutTime", faultOutTime);
        return result;
    }

    /**
     * 预警，故障指派他人增加批量处理
     *
     * @param param
     * @return
     */
    @Override
    public ResultMsg appointOthers(Map<String, Object> param, HttpServletRequest request) {
        //Map<String,Object> param = ServletsUtil.getParameters(request);
        String id = (String) param.get("id");
        String infoType = (String) param.get("infoType");
        String repairmanId = (String) param.get("repairmanId");
        String userId = request.getHeader("xfUserId");//消防userId
        final val userInfo = osUserService.getById(userId);
        try {
            if (id.contains(",")) {
                String[] ids = id.split(",");
                for (String s : ids) {
                    handleAppointOthers(s, infoType, repairmanId, userInfo);
                }
            } else {
                handleAppointOthers(id, infoType, repairmanId, userInfo);
            }
            return ResultMsg.getResultMsg("操作成功", 200);
        } catch (Exception e) {
            return ResultMsg.getResultMsg("操作失败", 10000);
        }

    }

    /**
     * 预警，故障指派他人
     *
     * @param id
     * @param infoType
     * @param repairmanId
     */
    public void handleAppointOthers(String id, String infoType, String repairmanId, OsUser osUser) {
        String nowTimw = DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
        try {

            FaultWarningTransferLog faultTransferLog = new FaultWarningTransferLog();
            faultTransferLog.setId(UUIDUtils.getUUID());
            faultTransferLog.setInfoId(id);
            if (cn.hutool.core.util.ObjectUtil.isNotEmpty(osUser)) {
                faultTransferLog.setStartUserId(osUser.getUserId());
            }
            faultTransferLog.setEndUserId(repairmanId);
            faultTransferLog.setTransferTime(nowTimw);

            if (StringUtils.equals(infoType, "1")) {
                //预警
                //设置消息类型为预警信息
                faultTransferLog.setInfoType(infoType);

                //跟新预警信息
                FireInfo fireInfo = fireInfoMapper.selectById(id);
                fireInfo.setSendTime(nowTimw);
                fireInfo.setFireStatus("2");
                fireInfo.setRepairmanId(repairmanId);
                fireInfoMapper.updateById(fireInfo);
            } else {
                //故障

                //设置消息类型为故障信息
                faultTransferLog.setInfoType(infoType);

                //跟新故障表，状态置为处理中
                FaultInfo faultInfo = faultInfoMapper.selectById(id);
                if (cn.hutool.core.util.ObjectUtil.isEmpty(faultInfo)) {
                    log.warn("未查询到故障表数据，查询主键未{}", id);
                    return;
                }
                faultInfo.setFaultStatus("2");
                faultInfo.setFaultStatusStr("处理中");
                faultInfo.setHandleStatus("处理中");
                if (cn.hutool.core.util.ObjectUtil.isNotEmpty(osUser)) {
                    faultInfo.setHandleUserId(osUser.getUserId());
                    faultInfo.setHandleUser(osUser.getFullname());
                    faultInfo.setSendUser(osUser.getFullname());
                }
                faultInfo.setHandleTime(DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
                faultInfo.setSendTime(nowTimw);
                faultInfo.setRepairmanId(repairmanId);
                String repairman = "";
                if (repairmanId.contains(",")) {
                    String[] repairmanIds = repairmanId.split(",");
                    for (String s : repairmanIds) {
                        OsUser user = osUserService.getById(s);
                        repairman += user.getFullname() + ",";
                    }
                    repairman = repairman.substring(0, repairman.length() - 1);
                } else {
                    OsUser user = osUserService.getById(repairmanId);
                    repairman = user.getFullname();
                }
                faultInfo.setHandleUser(repairman);
                faultInfoMapper.updateById(faultInfo);
            }
            //
            faultWarningTransferLogMapper.insert(faultTransferLog);
        } catch (Exception e) {
            e.printStackTrace();

        }
    }

    /**
     * 获取维保人员信息
     *
     * @param request
     * @return
     */
    @Override
    public List<Map<String, Object>> getPersonnelInfo(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);
//        // 建筑物id
//        String buildingId = (String) param.get("buildingId");
//        // 维保人员名称
//        String wbPerName = (String) param.get("wbPerName");
//        // 维保团队id
//        String wbTeamId = (String) param.get("wbTeamId");
//        if (StringUtils.isBlank(buildingId)) {
//            throw new RuntimeException("建筑物id不存在");
//        }
//        // 建筑物来进行所在组织的获取
//        BaseBuilding baseBuilding = baseBuildingMapper.selectById(buildingId);
//        if (null == baseBuilding) {
//            throw new RuntimeException("建筑物不存在");
//        }
//        List<OsUserDto> wbUser = osUserClient.getWbUser(baseBuilding.getBelongDep(), wbTeamId, wbPerName);
//        List<Map<String, Object>> result = Lists.newArrayList();
//        wbUser.forEach(user -> {
//            Map<String, Object> map = Maps.newHashMap();
//            map.put("wbPerId", user.getUserId());
//            map.put("wbPerName", user.getFullName());
//            map.put("wbPerTel", user.getMobile());
//            map.put("wbTeamName", user.getWbTeanName());
//            result.add(map);
//        });
//
//
//        return result;

//        return firePageWindowMapper.selectPersonnelInfo(param);
        return new ArrayList<>();
    }

    /**
     * 获取当前建筑物维保单位名称
     *
     * @param request
     * @return
     */
    @Override
    public List<Map<String, Object>> getWbTeamInfo(HttpServletRequest request) {
//        Map<String, Object> param = ServletsUtil.getParameters(request);
//        String buildingId = (String) param.get("buildingId");
//        if (StringUtils.isBlank(buildingId)) {
//            throw new RuntimeException("建筑物id不存在");
//        }
//        // 建筑物来进行所在组织的获取
//        BaseBuilding baseBuilding = baseBuildingMapper.selectById(buildingId);
//        List<Map<String, Object>> list = orgClient.getWbTeamByGroupId(baseBuilding.getBelongDep());
//        return list;
//        return firePageWindowMapper.selectWbTeamInfo(param);
        return new ArrayList<>();
    }

    /*
        故障忽略批量处理
     */
    @Override
    public ResultMsg faultOverLook(HttpServletRequest request) throws ParseException {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        String id = (String) param.get("id");
        ResultMsg rm = new ResultMsg();
        if (id.contains(",")) {
            String[] ids = id.split(",");
            for (String s : ids) {
                rm = handFaultOverLook(request, s);
                if (rm.getCode() == 10000) {
                    break;
                }
            }
        } else {
            rm = handFaultOverLook(request, id);
        }
        return rm;
    }

    public ResultMsg handFaultOverLook(HttpServletRequest request, String id) throws ParseException {
        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        String userId = request.getHeader("xfUserId");//消防userId
        UsersResponse.DataDTO dataDto = orgMiddleService.findUserByUserId(wztUserId);
        FaultInfo faultInfo = faultInfoMapper.selectById(id);
        String startTime = getPaseDate(7, 0, faultInfo.getReportedTime(), "yyyy-MM-dd HH:mm:ss");
        List<FaultInfo> faultInfos = faultInfoMapper.selectList(new QueryWrapper<FaultInfo>().eq("building_id", faultInfo.getBuildingId())
                .eq("point_id", faultInfo.getPointId())
                .eq("is_over_look", "1")
                //大于等于
                .ge("reported_time", startTime)
                //小于
                .lt("reported_time", DateUtil.formatDate(faultInfo.getReportedTime(), "yyyy-MM-dd HH:mm:ss")));
        if (faultInfos != null && faultInfos.size() > 0) {
            return ResultMsg.getResultMsg("此点位故障7天内不可再次忽略", 10000);
        }
        //更新故障表处理状态
        faultInfo.setFaultStatus("1");
        faultInfo.setFaultStatusStr("已处理");
        faultInfo.setIsOverLook("1");
        faultInfo.setHandleUserId(userId);
        faultInfo.setHandleUser(dataDto.getFullName());
        faultInfo.setHandleTime(DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
        faultInfo.setHandleStatus("已忽略");
        faultInfoMapper.updateById(faultInfo);
        //FastJSONUtils.toBean(redisUtils.get("point_status:"+fireInfo.getBuildingId() + "-" + fireInfo.getPointId()) +"", FarEastoneCache.class);
        FarEastoneCache farEastoneCache = FastJSONUtils.toBean(redisUtils.get("point_status:" + faultInfo.getBuildingId() + "-" + faultInfo.getPointId()) + "", FarEastoneCache.class);
        if (farEastoneCache != null) {
            farEastoneCache.setFaultHandleStatus("1");
            redisUtils.set("point_status:" + faultInfo.getBuildingId() + "-" + faultInfo.getPointId(), JSON.toJSONString(farEastoneCache));
        }
        return ResultMsg.getResultMsg("忽略成功", 200);
    }

    /**
     * 预警完成处理批量处理
     *
     * @param request
     * @return
     */
    @Override
    public ResultMsg warningFigureOut(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        String id = (String) param.get("id");
        if (id.contains(",")) {
            String[] ids = id.split(",");
            for (String s : ids) {
                handleWarningFigureOut(request, s);
            }
        } else {
            handleWarningFigureOut(request, id);
        }
        return ResultMsg.getResultMsg("操作成功", 200);
    }

    public void handleWarningFigureOut(HttpServletRequest request, String id) {
        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        String userId = request.getHeader("xfUserId");//消防userId
        UsersResponse.DataDTO dataDto = orgMiddleService.findUserByUserId(wztUserId);
        FireInfo fireInfo = fireInfoMapper.selectById(id);
        /*FarEastoneCache farEastoneCache = (FarEastoneCache)redisUtils.get("point_status:"+fireInfo.getBuildingId() + "-" + fireInfo.getPointId());
        if(StringUtils.equals(farEastoneCache.)){

        }*/
        fireInfo.setFireStatus("1");
        fireInfo.setExecuteResult("已处理");
        fireInfo.setExecutorId(userId);
        fireInfo.setExecutor(dataDto.getFullName());
        fireInfo.setExecuteTime(DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
        fireInfoMapper.updateById(fireInfo);

        //更新redis状态
        FarEastoneCache farEastoneCache = FastJSONUtils.toBean(redisUtils.get("point_status:" + fireInfo.getBuildingId() + "-" + fireInfo.getPointId()) + "", FarEastoneCache.class);
        if (farEastoneCache != null) {
            farEastoneCache.setFireHandleStatus("1");
            redisUtils.set("point_status:" + fireInfo.getBuildingId() + "-" + fireInfo.getPointId(), JSON.toJSONString(farEastoneCache));
            //return ResultMsg.getResultMsg("处理异常,请联系总部管理员!", 10000);
        }

        //return ResultMsg.getResultMsg("操作成功", 200);
    }

    /**
     * 添加excel单元格内容并居中
     *
     * @author: lf
     * @param: workbook
     * @return: org.apache.poi.hssf.usermodel.HSSFCell
     */
    public HSSFCell cellCenter(HSSFWorkbook workbook, HSSFCell cell, String str) {
        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);//居中
        cell.setCellStyle(style);
        cell.setCellValue(str);
        return cell;
    }


    /**
     * 火警误报统计折线图导出
     *
     * @param: request
     * @return:
     */
    @Override
    public void exportWBStatistic(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        String period = String.valueOf(param.get("period"));
        String buildingId = String.valueOf(param.get("buildingId"));
        //查询火警总点位数量
//        int num = baseDevicePointMapper.selectCount(new QueryWrapper<BaseDevicePoint>().eq("super_type", "0").eq("building_id", buildingId));
        List<String> periotList = new ArrayList<>();
        if (StringUtils.equals(period, "0")) {
            //统计7天
            periotList = getPeriotDate(7);
        } else {
            //统计30天
            periotList = getPeriotDate(30);
        }
        List<Map<String, Object>> result = new ArrayList<>();

        param.put("types", "2");
        for (String p : periotList) {
            param.put("todayDate", p);
            List<Map<String, Object>> faultFireStatistics = faultFireStatisticsMapper.selectStatisticsByDateList(param);
            if (!CollectionUtils.isEmpty(faultFireStatistics)) {
                result.add(faultFireStatistics.get(0));
            } else {
                Map<String, Object> map = new HashMap<>();
                map.put("day", p);
                map.put("alarmNum", "0");
                map.put("pointNum", "0");
                map.put("alarmRate", "0");
                result.add(map);
            }
        }
//        for (String day : periotList) {
//            Map<String, Object> map = new HashMap<>();
//            param.put("startTime", day + " 00:00:00");
//            param.put("endTime", day + " 23:59:59");
//            //查询当日报警总数
//            int alarmNum = fireInfoMapper.selectCount(new QueryWrapper<FireInfo>().eq("fire_type", "0").eq("building_id", buildingId)
//                    .ge("last_time", day + " 00:00:00").le("last_time", day + " 23:59:59"));
//            //当天时间前7天的时间
//            try {
//                Date date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(day + " 23:59:59");
//                Calendar calendar = Calendar.getInstance();
//                calendar.setTime(date);
//                calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - 7);
//                Date today = calendar.getTime();
//                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                param.put("beforSevenDayTime", format.format(today));
//                //重新设置结束时间格式
//                param.put("endTime", day + " 23:59:59");
//            } catch (ParseException e) {
//                e.printStackTrace();
//            }
//            Date date = DateUtil.parseDate(day, "yyyy-MM-dd");
//            int reusePointNum = repeatedlyMisinformationService.queryMisReportesList(buildingId, date);
//            String alarmRate = "0";
//            if (reusePointNum != 0 && num != 0) {
//                //计算结果乘100，四舍五入保留10位小数
//                alarmRate = new BigDecimal(reusePointNum + "").divide(new BigDecimal(num + ""), 10, RoundingMode.HALF_UP).multiply(new BigDecimal(100)) + "";
//            }
//            map.put("day", day);
//            map.put("alarmNum", alarmNum + "");
//            map.put("pointNum", reusePointNum + "");
//            map.put("alarmRate", alarmRate);
//            result.add(map);
//        }

        //数据填充
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("sheet");

        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        //style.setVerticalAlignment(HorizontalAlignment.VERTICAL_CENTER);

        //表头
        HSSFRow row0 = sheet.createRow(0);
        cellCenter(workbook, row0.createCell(0), "时间");
        cellCenter(workbook, row0.createCell(1), "误报数量");
        cellCenter(workbook, row0.createCell(2), "误报率(%)");
        cellCenter(workbook, row0.createCell(3), "现存重复误报点位");

        int index = 1;//下标
        for (Map<String, Object> map : result) {
            HSSFRow row = sheet.createRow(index++);
            cellCenter(workbook, row.createCell(0), map.get("day") + "");//时间
            cellCenter(workbook, row.createCell(1), map.get("alarmNum") + "");//误报数量
            cellCenter(workbook, row.createCell(2), map.get("alarmRate") + "");//误报率
            cellCenter(workbook, row.createCell(3), map.get("pointNum") + "");//现存重复误报点位
        }


        OutputStream output = null;

        try {
            output = response.getOutputStream();
        } catch (IOException e) {
            e.printStackTrace();
        }
        String fileName = DateUtils.formatDatetime(new Date()) + ".xls";
        response.reset();
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
        response.flushBuffer();
        workbook.write(output);
        output.close();
    }

    /**
     * 故障统计折线图导出
     *
     * @param: request, response
     * @return: void
     */
    @Override
    public void exportFaultStatistic(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        String period = String.valueOf(param.get("period"));
        String buildingId = String.valueOf(param.get("buildingId"));
        //查询火警总点位数量
        int num = baseDevicePointMapper.selectCount(new QueryWrapper<BaseDevicePoint>().eq("super_type", "0").eq("building_id", buildingId));
        List<String> periotList = new ArrayList<>();
        if (StringUtils.equals(period, "0")) {
            //统计7天
            periotList = getPeriotDate(7);
        } else {
            //统计30天
            periotList = getPeriotDate(30);
        }
        List<Map<String, Object>> result = new ArrayList<>();

        param.put("types", "1");
        for (String p : periotList) {
            param.put("todayDate", p);
            List<Map<String, Object>> faultFireStatistics = faultFireStatisticsMapper.selectStatisticsByDateList(param);
            if (!CollectionUtils.isEmpty(faultFireStatistics)) {
                result.add(faultFireStatistics.get(0));
            } else {
                Map<String, Object> map = new HashMap<>();
                map.put("day", p);
                map.put("faultNum", "0");
                map.put("faultPointNum", "0");
                map.put("faultRate", "0");
                result.add(map);
            }
        }
//        param.put("types","1");
//        param.put("todays",periotList);
////        List<FaultFireStatistics> faultFireStatistics = faultFireStatisticsMapper.selectList(new QueryWrapper<FaultFireStatistics>()
////                .eq("building_id",buildingId).eq("types","1").in("today_date",periotList));
//        List<Map<String,Object>> faultFireStatistics = faultFireStatisticsMapper.selectStatisticsList(param);
//        if (faultFireStatistics.size() == periotList.size()){
//
//        }
//        for (String day : periotList) {
//            Map<String, Object> map = new HashMap<>();
//            //当天时间范围
//            //param.put("startTime", periotList.get(0) + " 00:00:00");
//            param.put("endTime", day + " 23:59:59");
//            int faultNum = faultInfoMapper.selectCount(new QueryWrapper<FaultInfo>()
//                    .eq("building_id", buildingId).ge("last_time", day + " 00:00:00").le("last_time", day + " 23:59:59"));
//            int faultPointNum = firePageWindowMapper.selectFaultPointNum(param);
//
//            //故障率
//            String faultRate = "0";
//            if (faultPointNum != 0 && num != 0) {
//                //计算结果乘100，四舍五入保留10位小数
//                faultRate = new BigDecimal(faultPointNum + "").divide(new BigDecimal(num + ""), 10, RoundingMode.HALF_UP).multiply(new BigDecimal(100)) + "";
//            }
//
//            map.put("day", day);
//            map.put("faultNum", faultNum + "");
//            map.put("faultPointNum", faultPointNum + "");
//            map.put("faultRate", faultRate);
//            result.add(map);
//        }

        //数据填充
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("sheet");

        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        //style.setVerticalAlignment(HorizontalAlignment.VERTICAL_CENTER);

        //表头
        HSSFRow row0 = sheet.createRow(0);
        cellCenter(workbook, row0.createCell(0), "时间");
        cellCenter(workbook, row0.createCell(1), "故障总数");
        cellCenter(workbook, row0.createCell(2), "故障率(%)");
        cellCenter(workbook, row0.createCell(3), "现存故障数");

        int index = 1;//下标
        for (Map<String, Object> map : result) {
            HSSFRow row = sheet.createRow(index++);
            cellCenter(workbook, row.createCell(0), map.get("day") + "");//时间
            cellCenter(workbook, row.createCell(1), map.get("faultNum") + "");//故障总数
            cellCenter(workbook, row.createCell(2), map.get("faultRate") + "");//故障率
            cellCenter(workbook, row.createCell(3), map.get("faultPointNum") + "");//现存故障数
        }


        OutputStream output = null;

        try {
            output = response.getOutputStream();
        } catch (IOException e) {
            e.printStackTrace();
        }
        String fileName = DateUtils.formatDatetime(new Date()) + ".xls";
        response.reset();
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
        response.flushBuffer();
        workbook.write(output);
        output.close();
    }

    public String getTrueExpectionType(String str) {
        //异常类型（0手动，1断电，2运行，3设备故障，4设备离线）
        if (StringUtils.isNotBlank(str)) {
            if (StringUtils.equals("0", str)) {
                return "手动";
            }
            if (StringUtils.equals("1", str)) {
                return "断电";
            }
            if (StringUtils.equals("2", str)) {
                return "运行";
            }
            if (StringUtils.equals("3", str)) {
                return "设备故障";
            }
            if (StringUtils.equals("4", str)) {
                return "设备离线";
            }
        }
        return "";
    }

    public String getTrueExpectionStatus(String str) {
        //处理类型（0未处理，1已处理，2暂不处理）
        if (StringUtils.isNotBlank(str)) {
            if (StringUtils.equals("0", str)) {
                return "未处理";
            }
            if (StringUtils.equals("1", str)) {
                return "已处理";
            }
            if (StringUtils.equals("2", str)) {
                return "暂不处理";
            }
        }
        return "";
    }

    /**
     * 水压异常信息列表导出
     *
     * @param request
     * @param response
     * @param: request
     * @return: void
     */
    @Override
    public void exportWaterExpection(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        List<Map<String, Object>> waterExpectionList = firePageWindowMapper.getWaterExpectionListWithoutPage(param);
        //获取完整分页信息

        //数据填充
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("sheet");

        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        //style.setVerticalAlignment(HorizontalAlignment.VERTICAL_CENTER);

        //表头
        HSSFRow row0 = sheet.createRow(0);
        cellCenter(workbook, row0.createCell(0), "序号");
        cellCenter(workbook, row0.createCell(1), "点位号");
        cellCenter(workbook, row0.createCell(2), "点位描述");
        cellCenter(workbook, row0.createCell(3), "设备类型");
        cellCenter(workbook, row0.createCell(4), "异常类型");
        cellCenter(workbook, row0.createCell(5), "异常上报时间");
        cellCenter(workbook, row0.createCell(6), "处理类型");
        cellCenter(workbook, row0.createCell(7), "暂不处理结束时间");


        int index = 1;//下标
        for (Map<String, Object> map : waterExpectionList) {
            HSSFRow row = sheet.createRow(index);

            cellCenter(workbook, row.createCell(0), index + "");//序号
            cellCenter(workbook, row.createCell(1), !StringUtils.equals("null", map.get("pointCode") + "") ? map.get("pointCode") + "" : "");//点位号
            cellCenter(workbook, row.createCell(2), !StringUtils.equals("null", map.get("pointDesc") + "") ? map.get("pointDesc") + "" : "");//点位描述
            cellCenter(workbook, row.createCell(3), !StringUtils.equals("null", map.get("devType") + "") ? map.get("devType") + "" : "");//设备类型
            cellCenter(workbook, row.createCell(4), getTrueExpectionType(map.get("expectionType") + ""));//异常类型（0手动，1断电，2运行，3设备故障，4设备离线）
            cellCenter(workbook, row.createCell(5), !StringUtils.equals("null", map.get("reportTime") + "") ? map.get("reportTime") + "" : "");//异常上报时间
            cellCenter(workbook, row.createCell(6), getTrueExpectionStatus(map.get("expectionStatus") + ""));//处理类型（0未处理，1已处理，2暂不处理）
            cellCenter(workbook, row.createCell(7), !StringUtils.equals("null", map.get("endTime") + "") ? map.get("endTime") + "" : "");//暂不处理结束时间

            index++;
        }


        OutputStream output = null;

        try {
            output = response.getOutputStream();
        } catch (IOException e) {
            e.printStackTrace();
        }
        String fileName = DateUtils.formatDatetime(new Date()) + ".xls";
        response.reset();
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
        response.flushBuffer();
        output.close();
    }

    /**
     * 1.9.1.5水泵异常信息列表导出
     *
     * @param request response
     * @return
     */
    @Override
    public void exportPumpExpInfo(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Map<String, Object> param = ServletsUtil.getParameters(request);
//        param.put("startTime", DateUtil.formatDate(new Date(), "yyyy-MM-dd") + " 00:00:00");
//        param.put("endTime", DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
        List<Map<String, Object>> pumpExpectionList = firePageWindowMapper.getPumpExpectionListWithoutPage(param);

        //数据填充
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("sheet");

        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        //style.setVerticalAlignment(HorizontalAlignment.VERTICAL_CENTER);

        //表头
        HSSFRow row0 = sheet.createRow(0);
        cellCenter(workbook, row0.createCell(0), "序号");
        cellCenter(workbook, row0.createCell(1), "点位号");
        cellCenter(workbook, row0.createCell(2), "点位描述");
        cellCenter(workbook, row0.createCell(3), "设备类型");
        cellCenter(workbook, row0.createCell(4), "异常类型");
        cellCenter(workbook, row0.createCell(5), "异常上报时间");
        cellCenter(workbook, row0.createCell(6), "处理类型");
        cellCenter(workbook, row0.createCell(7), "暂不处理结束时间");


        int index = 1;//下标
        for (Map<String, Object> map : pumpExpectionList) {
            HSSFRow row = sheet.createRow(index);

            cellCenter(workbook, row.createCell(0), index + "");//序号
            cellCenter(workbook, row.createCell(1), !StringUtils.equals("null", map.get("pointCode") + "") ? map.get("pointCode") + "" : "");//点位号
            cellCenter(workbook, row.createCell(2), !StringUtils.equals("null", map.get("pointDesc") + "") ? map.get("pointDesc") + "" : "");//点位描述
            cellCenter(workbook, row.createCell(3), !StringUtils.equals("null", map.get("devType") + "") ? map.get("devType") + "" : "");//设备类型
            cellCenter(workbook, row.createCell(4), getTrueExpectionType(map.get("expectionType") + ""));//异常类型（0手动，1断电，2运行，3设备故障，4设备离线）
            cellCenter(workbook, row.createCell(5), !StringUtils.equals("null", map.get("reportTime") + "") ? map.get("reportTime") + "" : "");//异常上报时间
            cellCenter(workbook, row.createCell(6), getTrueExpectionStatus(map.get("expectionStatus") + ""));//处理类型（0未处理，1已处理，2暂不处理）
            cellCenter(workbook, row.createCell(7), !StringUtils.equals("null", map.get("endTime") + "") ? map.get("endTime") + "" : "");//暂不处理结束时间

            index++;
        }


        OutputStream output = null;

        try {
            output = response.getOutputStream();
        } catch (IOException e) {
            e.printStackTrace();
        }
        String fileName = DateUtils.formatDatetime(new Date()) + ".xls";
        response.reset();
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
        response.flushBuffer();
        workbook.write(output);
        output.close();
    }

    /**
     * app首页信息统计
     *
     * @param request
     * @return
     */
    @Override
    public Map<String, Integer> appFistPageStatistic(HttpServletRequest request) {
//        List<String> belongDeps = orgClient.getSquareGroupIds(ContextUtil.getCurrentUser().getDeptId());
//        List<String> roles = ContextUtil.getCurrentUser().getRoles();
//        List<String> groups = orgClient.selectByGroupIds(roles);
        //获取数据权限
        //万中台userId
        String wztUserId = request.getHeader("Wzt-Userid");
        String xfUserId = request.getHeader("xfUserId");//消防userId
        // 通过中台获取到用户对应建筑id集合
        List<String> squareGroupIds = userDataPowerService.findUserDataPower(wztUserId);
        //通过用户获取角色集合
        List<String> groups = osUserService.selectRoleKeyByWztUserId(wztUserId);
        //初始化数据
        int fire = 0, fault = 0, water = 0, pump = 0; int gasFire = 0;
        int closeStore = 0;

        if (ObjectUtil.listNotEmptyVerify(squareGroupIds)) {
            Map<String, Object> param = new HashMap<>();
            param.put("belongDeps", squareGroupIds);
            param.put("nowTime", DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
            if (groups.contains(UserRoleConstants.SITE_DISPOSAL)) {
                //现场处置用户首页展示条数
                fire = fireAlarmMapper.selectUnFeedCountAllBuild(param);
            } else {
                fire = firePageWindowMapper.selectFireUndoneCountAllBuild(param);
            }

            if (groups.contains(UserRoleConstants.SITE_DISPOSAL)) {
                //现场处置用户首页展示条数
                param.put("module","7");
                gasFire = fireAlarmMapper.selectUnFeedCountAllBuild(param);
            } else {
                gasFire = firePageWindowMapper.selectFireUndoneCountAllBuild(param);
            }


            if (groups.contains(UserRoleConstants.MAINT_USER)) {
                //维保人员首页展示条数
                param.put("wbId", xfUserId);
                fault = faultInfoMapper.getFaultOrFireInfoCountAllBuild(param);
            } else {
                fault = faultInfoMapper.getFaultToDealCountAllBuild(param);
            }
            water = waterAbnormalMapper.getCountsAllBuild(param);
            pump = statPumpExpectionMapper.getCountsAllBuild(param);
            closeStore = statCloseStoreExpectionMapper.getCountsAllBuild(param);
        }

        Map<String, Integer> result = new HashMap<>();
        result.put("fire", fire);
        result.put("fault", fault);
        result.put("water", water);
        result.put("pump", pump);
        result.put("gasFire", pump);
        result.put("closeStore", closeStore);
        return result;
//        return new HashMap<>();
    }

    @Autowired
    private ElectricalFireMonitoringService electricalFireMonitoringService;
    @Autowired
    private GasAlarmDeviceService gasAlarmDeviceService;

    /**
     * app首页水压水泵火警预警建筑列表
     *
     * @param: request
     * @return: map
     */
    @Override
    public ResultMsg appFistPageBuildingList(HttpServletRequest request, Map<String, Object> param) {

        // 0火警,1故障,2水压,3水泵,5闭店,6 电气火灾 7燃气 8 风机柜
        String module = (String) param.get("module");
        if("6".equals(module)){
            QueryData queryData = new QueryData();
            queryData.setPageNo(param.get("pageIndex") == null ? 1 : Integer.parseInt(param.get("pageIndex") + ""));
            queryData.setPageSize(param.get("pageSize") == null ? 10 : Integer.parseInt(param.get("pageSize") + ""));
            Map<String,String> params = new HashMap<>();
            params.put("buildingName",(String) param.get("buildingName"));
            queryData.setParams(params);
            JsonPageResult jsonPageResult = electricalFireMonitoringService.squareStatistics(request, queryData);
            PageParam pageParam = PageParam.getPage(queryData.getPageNo(), queryData.getPageSize());
            pageParam.setTotalRows(jsonPageResult.getResult().getTotalCount());
            Map<String, Object> result = new HashMap<>();
            result.put("pageParam", pageParam);
            result.put("tableData", jsonPageResult.getResult().getData());
            result.put("totalCount", jsonPageResult.getResult().getTotalCount());
            return ResultMsg.getResultMsg("查询成功", result, 200);
        }


        if("8".equals(module)){
            QueryData queryData = new QueryData();
            queryData.setPageNo(param.get("pageIndex") == null ? 1 : Integer.parseInt(param.get("pageIndex") + ""));
            queryData.setPageSize(param.get("pageSize") == null ? 10 : Integer.parseInt(param.get("pageSize") + ""));
            Map<String,String> params = new HashMap<>();
            params.put("buildingName",(String) param.get("buildingName"));
            queryData.setParams(params);
            JsonPageResult jsonPageResult = waterMonitorService.getCabinetBuildingList(request, queryData);
            PageParam pageParam = PageParam.getPage(queryData.getPageNo(), queryData.getPageSize());
            pageParam.setTotalRows(jsonPageResult.getResult().getTotalCount());
            Map<String, Object> result = new HashMap<>();
            result.put("pageParam", pageParam);
            result.put("tableData", jsonPageResult.getResult().getData());
            result.put("totalCount", jsonPageResult.getResult().getTotalCount());
            return ResultMsg.getResultMsg("查询成功", result, 200);
        }

        if ("5".equals(module)) {
            String parentId = (String) param.get("parentId");
            Integer level = (Integer) param.get("level");
            String buildingName = (String) param.get("buildingName");
            String userId = request.getHeader("Wzt-Userid");

            if (StrUtil.isBlank(userId)) {
                return ResultMsg.getResultMsg("请求头无人员id", 200);
            }
            int pageIndex = param.get("pageIndex") == null ? 1 : Integer.parseInt(param.get("pageIndex") + "");
            int pageRows = param.get("pageRows") == null ? 10 : Integer.parseInt(param.get("pageRows") + "");
            final val projectByUser = orgPosClient.getProjectByUser(userId);
            log.info("手机app闭店有权限的广场：{}", JSON.toJSONString(projectByUser));
            Map<String, Object> map = new HashMap<>();
            List<Map<String, Object>> resultList = new ArrayList<>();
            if (cn.hutool.core.util.ObjectUtil.isNotEmpty(projectByUser) && projectByUser.getSuccess()) {
                List<MidProjectInfo> list = (List<MidProjectInfo>)projectByUser.getData();
                ObjectMapper mapper = new ObjectMapper();
                List<MidProjectInfo> listNew = mapper.convertValue(list, new TypeReference<List<MidProjectInfo>>() { });
                final val iterator = listNew.iterator();
                while (iterator.hasNext()) {
                    final val next = iterator.next();
                    if (StrUtil.isNotBlank(buildingName) && StrUtil.isNotBlank(next.getRelProjectsName())
                            && next.getRelProjectsName().indexOf(buildingName) == -1) {
                        iterator.remove();
                    }
                }
                List<MidProjectInfo> pageList = page(listNew, pageIndex, pageRows);
                List<String> collectAll = listNew.stream().map(MidProjectInfo::getOrgId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(pageList)) {
                    log.info("手机app闭店有权限的列表不为空，列表数据个数：{}", pageList.size());
                    /*Map<String, MidProjectInfo> buildingIdMap = listNew.stream().collect(Collectors.toMap(MidProjectInfo::getOrgId, Function.identity(), (v1, v2) -> v1));*/
                    // 查询异常数量
                    List<String> collect = pageList.stream().map(MidProjectInfo::getOrgId).collect(Collectors.toList());
                    param.put("buildList", collect);
                    log.info("手机app闭店列表不为空，查询异常条数,参数：{}", JSON.toJSONString(param));
                    List<ExceptionCountDto> baseBuildings = firePageWindowMapper.selectBaseBuildingByBelongDepsWithCloseStoreV2(param);
                    Map<String, ExceptionCountDto> collect1 = Collections.emptyMap();
                    if (!CollectionUtils.isEmpty(baseBuildings)) {
                        collect1 = baseBuildings.stream().collect(Collectors.toMap(ExceptionCountDto::getBuildingId, Function.identity(), (v1, v2) -> v1));
                    }
                    Map<String, Object> newParam = new HashMap<>();
                    param.put("buildList", collectAll);
                    int totalCount = firePageWindowMapper.selectCloseStoreTotal(newParam);
                    log.info("手机app闭店列表不为空，查询异常结果，总条数：{}， 结果：{}", totalCount, JSON.toJSONString(collect1));
                    Map<String, ExceptionCountDto> finalCollect = collect1;
                    pageList.forEach(baseB -> {
                        String buildingId = baseB.getOrgId();
                        String relProjectsName = baseB.getRelProjectsName();
                        ExceptionCountDto exceptionCountDto = finalCollect.get(buildingId);
                        HashMap<String, Object> hashMap = new HashMap<>();
                        hashMap.put("buildingName", relProjectsName);
                        hashMap.put("buildingId", buildingId);
                        hashMap.put("count", exceptionCountDto == null ? 0 : exceptionCountDto.getCount());
                        resultList.add(hashMap);
                    });
                    //获取完整分页信息
                    PageParam pageParam = PageParam.getPage(pageIndex, pageRows);
                    pageParam.setTotalRows(listNew.size());
                    Map<String, Object> result = new HashMap<>();
                    result.put("pageParam", pageParam);
                    result.put("tableData", resultList);
                    result.put("totalCount", totalCount);
                    return ResultMsg.getResultMsg("查询成功", result, 200);
                } else {
                    //获取完整分页信息
                    PageParam pageParam = PageParam.getPage(pageIndex, pageRows);
                    pageParam.setTotalRows(listNew.size());
                    Map<String, Object> result = new HashMap<>();
                    result.put("pageParam", pageParam);
                    result.put("tableData", resultList);
                    result.put("totalCount", 0);
                    return ResultMsg.getResultMsg("查询成功", result, 200);
                }
            }
            return ResultMsg.getResultMsg("查询成功", map, 200);
        } else {
            AppBuildingDTO dto = new AppBuildingDTO();
            dto.setModule(module);
            dto.setParentId((String) param.get("parentId"));
            dto.setLevel((Integer) param.get("level"));
            dto.setBuildingName((String) param.get("buildingName"));
            Page<?> page = PageParam.initPagination(Integer.parseInt(param.get("pageIndex") + ""), Integer.parseInt(param.get("pageRows") + ""));
            List<BaseBuilding> buildings = monitorConsoleService.getBuildingListForApp(request, dto);
            if (buildings.size() < 1) {
                return ResultMsg.getResultMsg("您未绑定建筑权限，请联系管理员", 200);
            }

            Map<String, BaseBuilding> buildingIdMap = buildings.stream().collect(Collectors.toMap(BaseBuilding::getId, Function.identity(), (v1, v2) -> v1));
            List<String> buildingList = buildings.stream().map(BaseBuilding::getId).collect(Collectors.toList());
            param.put("nowTime", DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
            //条件查询建筑物分页
            List<Map<String, Object>> baseBuildings;
            int totalCount = 0;
            List<Map<String, Object>> resultList = new ArrayList<>();
            param.put("buildList", buildingList);
            param.put("module", module);
            String todayData = DateUtil.formatDate(new Date(), "yyyy-MM-dd");

            if (StringUtils.equals("0", module) || StringUtils.equals("7", module)) {
                baseBuildings = firePageWindowMapper.selectBaseBuildingByBelongDepsWithFire(page, param);
                totalCount = fireInfoMapper.selectCount(new LambdaQueryWrapper<FireInfo>()
                        .in(FireInfo::getBuildingId, buildingList)
                        .eq(FireInfo::getFireStatus, "0")
                        .eq(FireInfo::getBuildingStatusStr, "正常")
                        .ge(FireInfo::getLastTime, todayData + " 00:00:01")
                        .le(FireInfo::getLastTime, todayData + " 23:59:59"));
                resultList.addAll(baseBuildings);
            } else if (StringUtils.equals("1", module)) {
                baseBuildings = firePageWindowMapper.selectBaseBuildingByBelongDepsWithFault(page, param);
                totalCount = faultInfoMapper.selectCount(new LambdaQueryWrapper<FaultInfo>()
                        .in(FaultInfo::getBuildingId, buildingList)
                        .eq(FaultInfo::getFaultStatus, "0")
                        .eq(FaultInfo::getIsManualReport, "0")
                        .eq(FaultInfo::getBuildingStatusStr, "正常")
                        .ge(FaultInfo::getLastTime, todayData + " 00:00:00")
                        .le(FaultInfo::getLastTime, todayData + " 23:59:59"));
                resultList.addAll(baseBuildings);
            } else if (StringUtils.equals("2", module)) {
                baseBuildings = firePageWindowMapper.selectBaseBuildingByBelongDepsWithWater(page, param);
                totalCount = firePageWindowMapper.selectWaterTotal(param);
                resultList.addAll(baseBuildings);
            } else if (StringUtils.equals("3", module)) {
                baseBuildings = firePageWindowMapper.selectBaseBuildingByBelongDepsWithPump(page, param);
                totalCount = firePageWindowMapper.selectPumpTotal(param);
                resultList.addAll(baseBuildings);
            } else if (StringUtils.equals("5", module)) {//闭店监测
                baseBuildings = firePageWindowMapper.selectBaseBuildingByBelongDepsWithCloseStore(page, param);
                totalCount = firePageWindowMapper.selectCloseStoreTotal(param);
                resultList.addAll(baseBuildings);
            } else {
                baseBuildings = baseBuildingMapper.selectBaseBuildingByBelongDeps(page, param);
                resultList.addAll(baseBuildings);
            }
            resultList.forEach(map -> {
                String buildingId = (String) map.get("buildingId");
                BaseBuilding baseBuilding = buildingIdMap.get(buildingId);
                map.put("buildingName", baseBuilding.getBuildingName());
                map.put("buildingId", baseBuilding.getId());
                map.put("operatingCnter", baseBuilding.getOperatingCenter());
                map.put("jurisdictionVal", baseBuilding.getJurisdictionVal());
                map.put("projectId", baseBuilding.getProjectId());
            });
            //获取完整分页信息
            PageParam pageParam = PageParam.getPage(Integer.parseInt(param.get("pageIndex") + ""), Integer.parseInt(param.get("pageRows") + ""));
            pageParam.setTotalRows(page.getTotal());
            Map<String, Object> result = new HashMap<>();
            result.put("pageParam", pageParam);
            result.put("tableData", resultList);
            result.put("totalCount", totalCount);
            return ResultMsg.getResultMsg("查询成功", result, 200);
        }
    }

    /**
     * app首页水压水泵火警预警建筑列表
     *
     * @param: request
     * @return: map
     */
    @Override
    public ResultMsg appFistPageBuildingListCloseStore(HttpServletRequest request, Map<String, Object> param) {
        AppBuildingDTO dto = new AppBuildingDTO();
        // 0火警,1故障,2水压,3水泵,5闭店
        String module = (String) param.get("module");

        String parentId = (String) param.get("parentId");
        Integer level = (Integer) param.get("level");
        String buildingName = (String) param.get("buildingName");
        String userId = request.getHeader("Wzt-Userid");

        if (StrUtil.isBlank(userId)) {
            return ResultMsg.getResultMsg("请求头无人员id", 200);
        }
        int pageIndex = param.get("pageIndex") == null ? 1 : Integer.parseInt(param.get("pageIndex") + "");
        int pageRows = param.get("pageRows") == null ? 10 : Integer.parseInt(param.get("pageRows") + "");
        final val projectByUser = orgPosClient.getProjectByUser(userId);
        log.info("手机app闭店有权限的广场：{}", JSON.toJSONString(projectByUser));
        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> resultList = new ArrayList<>();
        if (cn.hutool.core.util.ObjectUtil.isNotEmpty(projectByUser) && projectByUser.getSuccess()) {
            List<MidProjectInfo> list = (List<MidProjectInfo>)projectByUser.getData();
            ObjectMapper mapper = new ObjectMapper();
            List<MidProjectInfo> listNew = mapper.convertValue(list, new TypeReference<List<MidProjectInfo>>() { });
            final val iterator = listNew.iterator();
            while (iterator.hasNext()) {
                final val next = iterator.next();
                if (StrUtil.isNotBlank(buildingName) && StrUtil.isNotBlank(next.getRelProjectsName())
                        && next.getRelProjectsName().indexOf(buildingName) == -1) {
                    iterator.remove();
                }
            }
            List<MidProjectInfo> pageList = page(listNew, pageIndex, pageRows);
            List<String> collectAll = listNew.stream().map(MidProjectInfo::getOrgId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(pageList)) {
                log.info("手机app闭店有权限的列表不为空，列表数据个数：{}", pageList.size());
                /*Map<String, MidProjectInfo> buildingIdMap = listNew.stream().collect(Collectors.toMap(MidProjectInfo::getOrgId, Function.identity(), (v1, v2) -> v1));*/
                // 查询异常数量
                List<String> collect = pageList.stream().map(MidProjectInfo::getOrgId).collect(Collectors.toList());
                param.put("buildList", collect);
                log.info("手机app闭店列表不为空，查询异常条数,参数：{}", JSON.toJSONString(param));
                List<ExceptionCountDto> baseBuildings = firePageWindowMapper.selectBaseBuildingByBelongDepsWithCloseStoreV2(param);
                Map<String, ExceptionCountDto> collect1 = Collections.emptyMap();
                if (!CollectionUtils.isEmpty(baseBuildings)) {
                    collect1 = baseBuildings.stream().collect(Collectors.toMap(ExceptionCountDto::getBuildingId, Function.identity(), (v1, v2) -> v1));
                }
                Map<String, Object> newParam = new HashMap<>();
                param.put("buildList", collectAll);
                int totalCount = firePageWindowMapper.selectCloseStoreTotal(newParam);
                log.info("手机app闭店列表不为空，查询异常结果，总条数：{}， 结果：{}", totalCount, JSON.toJSONString(collect1));
                Map<String, ExceptionCountDto> finalCollect = collect1;
                pageList.forEach(baseB -> {
                    String buildingId = baseB.getOrgId();
                    String relProjectsName = baseB.getRelProjectsName();
                    ExceptionCountDto exceptionCountDto = finalCollect.get(buildingId);
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("buildingName", relProjectsName);
                    hashMap.put("buildingId", buildingId);
                    hashMap.put("count", exceptionCountDto == null ? 0 : exceptionCountDto.getCount());
                    resultList.add(hashMap);
                });
                //获取完整分页信息
                PageParam pageParam = PageParam.getPage(pageIndex, pageRows);
                pageParam.setTotalRows(listNew.size());
                Map<String, Object> result = new HashMap<>();
                result.put("pageParam", pageParam);
                result.put("tableData", resultList);
                result.put("totalCount", totalCount);
                return ResultMsg.getResultMsg("查询成功", result, 200);
            } else {
                //获取完整分页信息
                PageParam pageParam = PageParam.getPage(pageIndex, pageRows);
                pageParam.setTotalRows(listNew.size());
                Map<String, Object> result = new HashMap<>();
                result.put("pageParam", pageParam);
                result.put("tableData", resultList);
                result.put("totalCount", 0);
                return ResultMsg.getResultMsg("查询成功", result, 200);
            }
        }
        return ResultMsg.getResultMsg("查询成功", map, 200);

    }

    public void paramAddDate(Map<String, Object> param) {
        param.put("startTime", DateUtil.formatDate(new Date(), "yyyy-MM-dd") + " 00:00:00");
        param.put("endTime", DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
    }

    private <T> List<T> page(List<T> list, int pageNo, int pageSize) {
        int size = list.size();
        int l = (pageNo - 1) * pageSize;
        int r = pageNo * pageSize > size ? size : pageNo * pageSize;
        if (l >= size) {
            return new ArrayList<>();
        }
        return list.subList(l, r);
    }

    /**
     * 获取过去某时的日期
     *
     * @param
     * @return
     */
    public String getPaseDate(int day, int hour, Date date, String type) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - day);
        calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) - hour);
        Date today = calendar.getTime();
        SimpleDateFormat format = new SimpleDateFormat(type);
        String result = format.format(today);
        return result;
    }

    /**
     * 获取过去第几天的日期
     *
     * @param past
     * @return
     */
    public String getPastDate(int past, String type) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - past);
        Date today = calendar.getTime();
        SimpleDateFormat format = new SimpleDateFormat(type);
        String result = format.format(today);
        return result;
    }

    /**
     * 获取未来某时的日期
     *
     * @param
     * @return
     */
    public String getFutureDate(int day, int hour, Date date, String type) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) + day);
        calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) + hour);
        Date today = calendar.getTime();
        SimpleDateFormat format = new SimpleDateFormat(type);
        String result = format.format(today);
        return result;
    }

    /**
     * @param day      天
     * @param hour     小时
     * @param min      分钟
     * @param date     Date格式日期
     * @param type     0过去，1未来
     * @param timeType 时间格式
     * @return
     */
    public String getDatebyTime(Double day, Double hour, Double min, String date, String type, String timeType) {
        long time = 0;
        try {
            Long second = Double.valueOf((day * 24 * 60 * 60 + hour * 60 * 60 + min * 60) * 1000).longValue();
            Long dateTime = new SimpleDateFormat(timeType).parse(date).getTime();

            if ("0".equals(type)) {
                time = dateTime - second;

            } else if ("1".equals(type)) {
                time = dateTime + second;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new SimpleDateFormat(timeType).format(new Date(time));
    }

    /**
     * 根据建筑id获取建筑调试状态
     *
     * @param buildId
     * @return
     */
    @Override
    public boolean getBuildStatus(String buildId) {

        StoreDb storeDb = storeDbMapper.selectOne(new QueryWrapper<StoreDb>().eq("building_id", buildId));
        if (storeDb != null && "1".equals(storeDb.getBuildState())) {
            return false;
        }
        return true;
    }

    public String getBuildingStatus(BaseBuilding baseBuilding) {
        String buildingStatus = "正常";
        if (!getBuildStatus(baseBuilding.getId())) {
            buildingStatus = "调试";
        }
        //        try {
//            AppointmentApplyVo appointmentApplyVo = appointmentApplyServiceImpl.getMaintenanceApply(baseBuilding.getId());
//            String scheduling = appointmentApplyVo.getScheduling();
//            if (StringUtils.isNoneBlank(scheduling) && (StringUtils.equals("0",scheduling) || StringUtils.equals("2",scheduling) || StringUtils.equals("3",scheduling))){
//                buildingStatus = "维保";
//            }
//        }catch (Exception e){
//            e.printStackTrace();
//        }
        if ("1".equals(baseBuilding.getBuildingStatus())) {
            buildingStatus = "维保";
        }
        return buildingStatus;
    }

    @Override
    public ResultMsg getSingleInfo(String fireId) {
        return ResultMsg.getResultMsg("获取成功", getFireCheckInfo(fireId), 200);
    }

    @Override
    public ResultMsg getFireHistory(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);

        CassandraQueryDTO dto = new CassandraQueryDTO();
        dto.setBuildingId(param.get("buildingId").toString());
        dto.setPointId(param.get("pointId").toString());
        dto.setPageIndex(Integer.parseInt(param.get("pageIndex") + ""));
        dto.setPageRows(Integer.parseInt(param.get("pageRows") + ""));
        dto.setFirstTime(param.get("firstTime").toString());
        dto.setLastTime(param.get("lastTime").toString());
//        ResultMsg resultMsg = InfluxDataDao.selectObject(dto, CassandraConstants.GETFIREHISTORY, ResultMsg.class);
        return influxDataDao.selectObject(dto);
    }

    private List<PumpData> checkException(String pointId, String buildingId, String reportTimeStr) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date reportTime = null;
        try {
            reportTime = simpleDateFormat.parse(reportTimeStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(reportTime);
        calendar.add(Calendar.HOUR, 48); //  48小时内
        Date endTime = calendar.getTime();

        // 获取对应的水泵数据
        List<PumpData> pumpDataList = this.getPressureLinkPumpDatas(buildingId, pointId, reportTime, endTime);
        if (pumpDataList != null) {
            log.info("#### (resetException)  压力开发联锁水泵 = (pumpDataList.size() = " + pumpDataList.size() + ")");
        }
        return pumpDataList;
    }

    private List<PumpData> getPressureLinkPumpDatas(String buildingId, String pointId, Date startTime, Date endTime) {
        List<PumpData> pumpDataList = null;
        List<BaseDevicePoint> devicePoints;
        String powerStatus = "1";
        String manualStatus = "1";
        String runStatus = "1";
        String faultStatus = "0";
        // 查询压力开关对应的水泵设备。
        List<PressureLinkPumpInfo> pressureLinkPumpInfoList = this.getPressLinkPumpInfoList(buildingId, pointId);
        if (pressureLinkPumpInfoList != null && pressureLinkPumpInfoList.size() > 0) { // 有压力开关对应水泵的台账的。
            PressureLinkPumpInfo pressureLinkPumpInfo;
            for (int i = 0; i < pressureLinkPumpInfoList.size(); i++) {
                pressureLinkPumpInfo = pressureLinkPumpInfoList.get(i);
                // 根据设备类型找到相应的水泵
                String devTypeName = pressureLinkPumpInfo.getDevTypeName();
                devicePoints = this.getDevicePointByDevType(buildingId, devTypeName);
                if (devicePoints != null && devicePoints.size() > 0) {
                    for (int j = 0; j < devicePoints.size(); j++) {
                        String pumpPointId = devicePoints.get(j).getId(); // 泵的点位id
                        Map<String, String> redisData = (Map<String, String>) JSON.parse((String) redisUtils.get(buildingId + "_pump_" + pumpPointId));
                        //最近一次心跳时间
                        if ("1".equals(redisData.get("deviceStatus"))) {
                            continue;
                        }
                        if (pumpDataList == null) {
                            pumpDataList = preDataDao.findPumpDataByTime(buildingId, pumpPointId, startTime, endTime, powerStatus, manualStatus, runStatus, faultStatus);
                        } else {
                            pumpDataList.addAll(preDataDao.findPumpDataByTime(buildingId, pumpPointId, startTime, endTime, powerStatus, manualStatus, runStatus, faultStatus));
                        }
                    }
                }
            }
        }
        return pumpDataList;

    }

    private List<BaseDevicePoint> getDevicePointByDevType(String buildingId, String devTypeName) {
        QueryWrapper queryWrapper = new QueryWrapper();
        LambdaQueryWrapper<BaseDevicePoint> lambda = queryWrapper.lambda();
        lambda.eq(BaseDevicePoint::getDevTypeName, devTypeName);
        lambda.eq(BaseDevicePoint::getBuildingId, buildingId);
        lambda.eq(BaseDevicePoint::getSuperType, "1"); // 查询泵的条件
        return baseDevicePointMapper.selectList(queryWrapper);
    }

    private List<PressureLinkPumpInfo> getPressLinkPumpInfoList(String buildingId, String pointId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        LambdaQueryWrapper<PressureLinkPumpInfo> lambdaQueryWrapper = queryWrapper.lambda();
        lambdaQueryWrapper.eq(PressureLinkPumpInfo::getPointId, pointId);
        lambdaQueryWrapper.eq(PressureLinkPumpInfo::getBuildingId, buildingId);
        return pressureLinkPumpInfoMapper.selectList(queryWrapper);

    }

    public static void main(String[] args) throws ParseException {
        FirePageWindowServiceImpl fire = new FirePageWindowServiceImpl();
        String date = fire.getFutureDate(7, 0, new Date(), "yyyy-MM-dd HH:mm:ss");
        System.out.println(date);
        System.out.println(System.currentTimeMillis());
        System.out.println(fire.getDatebyTime(6.0, 0.0, 0.0, DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"), "0", "yyyy-MM-dd"));
        System.out.println(fire.getPeriotDate(6));
        System.out.println(DateUtils.minusDay(DateUtil.parseDate("2021-01-12 00:00:00"), DateUtil.parseDate("2021-01-06 23:59:59")));
        Integer in = 1;
        System.out.println(in.toString());
    }

    public void sendMessage(Map<String, Object> result) {
        try {
            String webServer1 = "*************"; // 发送端ip
            String webPort1 = "8199"; // 发送端端口
            String uri1 = "http://" + webServer1 + ":" + webPort1 + "/fire/alarm"; // 发送端的具体地址
            System.out.println("==== 真实火警上传，开始上传 ==== ");
            String postResult = httpClientUtil.sendPost(result, uri1);
            System.out.println("==== 真实火警上传，返回的结果 ==== " + postResult);
        } catch (Exception e) {
            e.printStackTrace();

        }
    }

    /**
     * 设置火警信息类型
     *
     * @param param
     */
    @Override
    public ResultMsg updateFireFocusInfo(HttpServletRequest request, Map<String, Object> param) {
        //Map param = ServletsUtil.getParameters(request);
        String fireFocusType = String.valueOf(param.get("fireFocusType"));
        String emergencyPlanId = String.valueOf(param.get("emergencyPlanId"));
        String remark = String.valueOf(param.get("remark"));
        String fireId = String.valueOf(param.get("id"));
        String buildingId = String.valueOf(param.get("buildingId"));
        String lastTime = String.valueOf(param.get("lastTime"));
        Date lastDate = DateUtil.parseDate(lastTime, "yyyy-MM-dd HH:mm:ss");
        String emergencyType = String.valueOf(param.get("emergencyType"));
        List<String> originList = getIdList(fireId);
        //如果是1条判断有没有处理过
        if (originList != null && originList.size() == 1) {
            FireInfo fireInfo = fireInfoMapper.selectById(originList.get(0));
            if ("1".equals(fireInfo.getFireStatus())) {
                return ResultMsg.getResultMsg("此火警已处理", 1, 200);
            }
        }

        List<String> idList = getSyncIds(originList);
        Integer syncCount = 0;
        for (String id : idList) {
            // 添加信息
            syncCount ++;
            RealFireFocusInfo info = new RealFireFocusInfo();
            info.setFireId(id);
            info.setId(IdGenerator.getIdStr());
            info.setFireType(fireFocusType);
            info.setEmergencyPlan(emergencyPlanId);
            info.setRemark(remark);
            info.setBuildingId(buildingId);
            info.setLastTime(lastDate);
            // 默认为未处理 0
            info.setRealFireStatus("0");
            info.setEmergencyType(emergencyType);
            log.info("updateFireFocusInfo,保存真实火警信息");
            realFireFocusInfoMapper.insert(info);
        }
//        try {
//            if(fireId.contains(",")){
//                //批量处理火警
//                String[] fireIds = fireId.split(",");
//                for (String id : fireIds) {
//                    handleFireInfo(id,fireType,param);
//                }
//            }else{
//                handleFireInfo(fireId,fireType,param);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        return ResultMsg.getResultMsg("处理成功", syncCount, 200);
    }

    @Override
    public List<WebsocketInfo> getFloorFireExpection(HttpServletRequest request) {
        Map<String, Object> param = ServletsUtil.getParameters(request);
        String fireId = String.valueOf(param.get("fireId"));
        //查火警
        List<FireInfo> fireInfos = new ArrayList<>();
        if (StringUtils.isEmpty(fireId)) {
            param.put("type", "0");
            fireInfos = fireInfoMapper.selectRealFireExpection(param);
        } else {
            FireInfo fireInfo = fireInfoMapper.selectById(fireId);
            if (fireInfo != null) {
                fireInfos.add(fireInfo);
            }
        }
        List<WebsocketInfo> result = new ArrayList<>();
        if (fireInfos != null && !fireInfos.isEmpty()) {
            // 先获取所有的点位id
            List<String> pids = fireInfos.stream().map(FireInfo::getPointId).collect(Collectors.toList());
            List<BaseDevicePoint> baseDevicePoints = baseDevicePointMapper.selectBatchIds(pids);
            if (!CollectionUtils.isEmpty(baseDevicePoints)) {
                Map<String, List<BaseDevicePoint>> pointMap = baseDevicePoints.stream().collect(Collectors.groupingBy(BaseDevicePoint::getId));
                for (FireInfo fireInfo : fireInfos) {
                    WebsocketInfo websocketInfo = new WebsocketInfo();
                    websocketInfo.setType("fire");
                    websocketInfo.setBuildingId((String) param.get("buildingId"));
                    HashMap<String, Object> data = new HashMap<>();
                    List<BaseDevicePoint> baseDevicePointList = pointMap.get(fireInfo.getPointId());
//                    BaseDevicePoint baseDevicePoint = (BaseDevicePoint) baseDevicePoints1
                    FloorInfo floorInfo = new FloorInfo();
                    if (!CollectionUtils.isEmpty(baseDevicePointList)) {
                        BaseDevicePoint baseDevicePoint = baseDevicePointList.get(0);
                        floorInfo.setId(baseDevicePoint.getFloorId());
                        floorInfo.setFloor(baseDevicePoint.getFloor() == null ? 0 : Integer.parseInt(baseDevicePoint.getFloor() + ""));
                        floorInfo.setName(baseDevicePoint.getFloorName());
                        floorInfo.setPointX(baseDevicePoint.getWide() == null ? 0.0 : Double.valueOf(baseDevicePoint.getWide() + ""));
                        floorInfo.setPointY(baseDevicePoint.getTall() == null ? 0.0 : Double.valueOf(baseDevicePoint.getTall() + ""));
                        floorInfo.setWztFloorId(baseDevicePoint.getWztFloorId());
                    }
                    data.put("floorInfo", floorInfo);
                    data.put("alarmInfo", fireInfo);
                    websocketInfo.setData(data);
                    result.add(websocketInfo);
                }
            }
            //火警
//            for (FireInfo fireInfo : fireInfos) {
//                WebsocketInfo websocketInfo = new WebsocketInfo();
//                websocketInfo.setType("fire");
//                websocketInfo.setBuildingId((String) param.get("buildingId"));
//                HashMap<String, Object> data = new HashMap<>();
//                BaseDevicePoint baseDevicePoint = baseDevicePointMapper.selectById(fireInfo.getPointId());
//                FloorInfo floorInfo = new FloorInfo();
//                if (baseDevicePoint != null) {
//                    floorInfo.setId(baseDevicePoint.getFloorId());
//                    floorInfo.setFloor(baseDevicePoint.getFloor() == null ? 0 : Integer.parseInt(baseDevicePoint.getFloor() + ""));
//                    floorInfo.setName(baseDevicePoint.getFloorName());
//                    floorInfo.setPointX(baseDevicePoint.getPointX() == null ? 0.0 : Double.valueOf(baseDevicePoint.getPointX() + ""));
//                    floorInfo.setPointY(baseDevicePoint.getPointY() == null ? 0.0 : Double.valueOf(baseDevicePoint.getPointY() + ""));
//                }
//                data.put("floorInfo", floorInfo);
//                data.put("alarmInfo", fireInfo);
//                websocketInfo.setData(data);
//                result.add(websocketInfo);
//            }
        }
        return result;
    }

    @Override
    public JsonPageResult getRealFirePage(QueryData queryData) {
        String wztUserId = queryData.getParams().get("wztUserId");//万中台userId
        String buildingId = queryData.getParams().get("buildingId");//万中台userId
        log.info("获取人员项目数据权限-------------------------入参getUserDataPower:" + JSON.toJSONString(queryData));
        List<String> projectIdList = new ArrayList<>();
        try {
            List<UsersResponse.DataDTO> users = orgMiddleService.findUsersByUsersIds(Arrays.asList(wztUserId));
            if (CollectionUtil.isEmpty(users)) {
                return JsonPageResult.getFail("无用户信息");
            }
            String userType = users.get(0).getUserType();
            if ("FWGSYG".equals(userType)) {
                Map<String, String> params = new HashMap<>();
                params.put("userId", users.get(0).getUserId());
                params.put("scope", "2");
                final val projectPermissionByUserId = orgMiddleService.getProjectPermissionByUserId(params);
                projectIdList.addAll(projectPermissionByUserId);
            } else if ("ZFJGRY".equals(userType)) {
                String orgId = users.get(0).getOrgId();
                if (StringUtils.isBlank(orgId)) {
                    return JsonPageResult.getFail("用户未绑定企业");
                }
                Map<String, Object> params = new HashMap<>();
                params.put("affiliationId", users.get(0).getOrgId());
                params.put("business", "3");
                final val projectByAffiliationId = orgMiddleService.getProjectByAffiliationId(params);
                if (CollectionUtil.isNotEmpty(projectByAffiliationId)) {
                    projectByAffiliationId.stream().map(item -> item.getProjectId()).forEach(projectIdList::add);
                }
            } else if ("WYGSYG".equals(userType)) {
                Map<String, String> params = new HashMap<>();
                params.put("userId", users.get(0).getUserId());
                params.put("scope", "1");
                final val projectPermissionByUserId = orgMiddleService.getProjectPermissionByUserId(params);
                projectIdList.addAll(projectPermissionByUserId);
            }
            if (CollectionUtil.isEmpty(projectIdList)) {
                return JsonPageResult.getFail("用户有权限的项目为空");
            }
        } catch (Exception e) {
            log.error("获取人员项目数据权限异常：", e);
            return JsonPageResult.getFail("获取人员项目数据权限异常");
        }
        log.info("获取人员项目数据权限-------------------------出参为:" + JSON.toJSONString(projectIdList));
        // 查询这些建筑的真实火警
        // 根据有权限的pg_项目查询火警信息中的buildingId，避免连表
        LambdaQueryWrapper<BaseBuilding> wrapper = Wrappers.lambdaQuery();
        wrapper.in(BaseBuilding::getProjectId, projectIdList);
        List<BaseBuilding> baseBuildings = baseBuildingMapper.selectList(wrapper);
        if (CollectionUtil.isEmpty(baseBuildings)) {
            return JsonPageResult.getFail("用户有权限的火警项目信息为空");
        }
        List<String> buildingIdList = baseBuildings.stream().map(BaseBuilding::getId).distinct().collect(Collectors.toList());
        QueryFilter queryFilter = QueryFilterBuilder.createQueryFilter(queryData);
        queryFilter.getParams().put("buildList", buildingIdList);
        if (StringUtils.isNotEmpty(buildingId)) {
            LambdaQueryWrapper<BaseBuilding> wrapper1 = Wrappers.lambdaQuery();
            wrapper1.eq(BaseBuilding::getProjectId, buildingId);
            List<BaseBuilding> baseBuildings1 = baseBuildingMapper.selectList(wrapper1);
            if (CollectionUtil.isEmpty(baseBuildings1)) {
                queryFilter.getParams().put("buildingId", null);
            } else {
                queryFilter.getParams().put("buildingId", baseBuildings1.get(0).getId());
            }
        }
        IPage<FireInfo> realFirePage = fireInfoMapper.getRealFirePage(queryFilter.getPage(), queryFilter.getParams());
        JsonPageResult result = JsonPageResult.getSuccess("");
        result.setPageData(realFirePage);
        return result;
    }

    public JsonResult getLatestFireRecord(String wztUserId) {
        JsonResult result = JsonResult.Success();
        HashMap<String, String> resultMap = new HashMap<>();
        resultMap.put("handleTime","0");
        resultMap.put("handleTimeRate","0");
        resultMap.put("feedbackTime","0");
        resultMap.put("feedbackTimeRate","0");
        resultMap.put("runTime","0");
        resultMap.put("runTimeRate","0");
        resultMap.put("realFireCount","0");
        resultMap.put("wrongFireCount","0");
        log.info("getLatestFireRecord 查询近30天灭火记录，用户id:{}", wztUserId);
        log.info("获取人员项目数据权限-------------------------入参getUserDataPower:" + wztUserId);
        List<String> projectIdList = new ArrayList<>();
        try {
            List<UsersResponse.DataDTO> users = orgMiddleService.findUsersByUsersIds(Arrays.asList(wztUserId));
            if (CollectionUtil.isEmpty(users)) {
                return JsonPageResult.getFail("无用户信息");
            }
            String userType = users.get(0).getUserType();
            if ("FWGSYG".equals(userType)) {
                Map<String, String> params = new HashMap<>();
                params.put("userId", users.get(0).getUserId());
                params.put("scope", "2");
                final val projectPermissionByUserId = orgMiddleService.getProjectPermissionByUserId(params);
                projectIdList.addAll(projectPermissionByUserId);
            } else if ("ZFJGRY".equals(userType)) {
                String orgId = users.get(0).getOrgId();
                if (StringUtils.isBlank(orgId)) {
                    return JsonPageResult.getFail("用户未绑定企业");
                }
                Map<String, Object> params = new HashMap<>();
                params.put("affiliationId", users.get(0).getOrgId());
                params.put("business", "3");
                final val projectByAffiliationId = orgMiddleService.getProjectByAffiliationId(params);
                if (CollectionUtil.isNotEmpty(projectByAffiliationId)) {
                    projectByAffiliationId.stream().map(item -> item.getProjectId()).forEach(projectIdList::add);
                }
            } else if ("WYGSYG".equals(userType)) {
                Map<String, String> params = new HashMap<>();
                params.put("userId", users.get(0).getUserId());
                params.put("scope", "1");
                final val projectPermissionByUserId = orgMiddleService.getProjectPermissionByUserId(params);
                projectIdList.addAll(projectPermissionByUserId);
            }
            if (CollectionUtil.isEmpty(projectIdList)) {
                return JsonPageResult.getFail("用户有权限的项目为空");
            }
        } catch (Exception e) {
            log.error("获取人员项目数据权限异常：", e);
            return JsonPageResult.getFail("获取人员项目数据权限异常");
        }
        log.info("获取人员项目数据权限-------------------------出参为:" + JSON.toJSONString(projectIdList));
        // 查询这些建筑的真实火警
        // 根据有权限的pg_项目查询火警信息中的buildingId，避免连表
        LambdaQueryWrapper<BaseBuilding> wrapper = Wrappers.lambdaQuery();
        wrapper.in(BaseBuilding::getProjectId, projectIdList);
        List<BaseBuilding> baseBuildings = baseBuildingMapper.selectList(wrapper);
        if (CollectionUtil.isEmpty(baseBuildings)) {
            return JsonPageResult.getFail("用户有权限的火警项目信息为空");
        }
        List<String> buildingIdList = baseBuildings.stream().map(BaseBuilding::getId).distinct().collect(Collectors.toList());
        Map<String, Object> map = new HashMap<>();
        map.put("buildList", buildingIdList);
        Date date = new Date();
        Date date1 = DateUtils.addDay(date, -30);
        map.put("startTime", DateUtil.formatDate(date, "yyyy-MM-dd"));
        map.put("endTime", DateUtil.formatDate(date1, "yyyy-MM-dd"));
        List<FireInfo> realFirePage = fireInfoMapper.getRealFirePage(map);
        Date date3 = DateUtils.addDay(date1, -1);
        Date date2 = DateUtils.addDay(date3, -30);
        Map<String, Object> map2 = new HashMap<>();
        map2.put("buildList", buildingIdList);
        map2.put("startTime", DateUtil.formatDate(date3, "yyyy-MM-dd"));
        map2.put("endTime", DateUtil.formatDate(date2, "yyyy-MM-dd"));
        List<FireInfo> realFirePageLastMonth = fireInfoMapper.getRealFirePage(map2);
        BigDecimal thisHandleMinutes = BigDecimal.ZERO;
        BigDecimal thisFeedbackMinutes = BigDecimal.ZERO;
        BigDecimal thisRunMinutes = BigDecimal.ZERO;
        BigDecimal lastHandleMinutes = BigDecimal.ZERO;
        BigDecimal lastFeedbackMinutes = BigDecimal.ZERO;
        BigDecimal lastRunMinutes = BigDecimal.ZERO;

        if (CollectionUtil.isNotEmpty(realFirePage)) {
            int totalHandleMinutes = 0;
            int totalFeedbackMinutes = 0;
            int totalRunSecond = 0;
            for (FireInfo obj : realFirePage) {
                String lastTime = obj.getLastTime();
                String executeTime = obj.getExecuteTime();
                String feedbackTimeStr = obj.getFeedbackTimeStr();
                totalHandleMinutes += getDiffMinutes(executeTime, lastTime);
                totalFeedbackMinutes += getDiffMinutes(feedbackTimeStr, lastTime);
                String duration = obj.getDuration();
                if (StringUtils.isNotBlank(duration)) {
                    totalRunSecond += Integer.parseInt(duration);
                }
            }
            BigDecimal handleDecimal = new BigDecimal(totalHandleMinutes);
            BigDecimal feedbackDecimal = new BigDecimal(totalFeedbackMinutes);
            BigDecimal runDecimal = new BigDecimal(totalRunSecond);
            thisHandleMinutes= handleDecimal.divide(new BigDecimal(realFirePage.size()), 1, BigDecimal.ROUND_HALF_UP);
            thisFeedbackMinutes = feedbackDecimal.divide(new BigDecimal(realFirePage.size()), 1, BigDecimal.ROUND_HALF_UP);
            thisRunMinutes = runDecimal.divide(new BigDecimal(realFirePage.size() * 60), 1, BigDecimal.ROUND_HALF_UP);
            resultMap.put("handleTime", thisHandleMinutes.stripTrailingZeros().toPlainString());
            resultMap.put("feedbackTime", thisFeedbackMinutes.stripTrailingZeros().toPlainString());
            resultMap.put("runTime", thisRunMinutes.stripTrailingZeros().toPlainString());
            resultMap.put("realFireCount", String.valueOf(realFirePage.size()));
        }

        if (CollectionUtil.isNotEmpty(realFirePageLastMonth)) {
            int totalHandleMinutes = 0;
            int totalFeedbackMinutes = 0;
            int totalRunSecond = 0;
            for (FireInfo obj : realFirePageLastMonth) {
                String lastTime = obj.getLastTime();
                String executeTime = obj.getExecuteTime();
                String feedbackTimeStr = obj.getFeedbackTimeStr();
                totalHandleMinutes += getDiffMinutes(executeTime, lastTime);
                totalFeedbackMinutes += getDiffMinutes(feedbackTimeStr, lastTime);
                String duration = obj.getDuration();
                if (StringUtils.isNotBlank(duration)) {
                    totalRunSecond += Integer.parseInt(duration);
                }
            }
            BigDecimal handleDecimal = new BigDecimal(totalHandleMinutes);
            BigDecimal feedbackDecimal = new BigDecimal(totalFeedbackMinutes);
            BigDecimal runDecimal = new BigDecimal(totalRunSecond);
            lastHandleMinutes = handleDecimal.divide(new BigDecimal(realFirePageLastMonth.size()), 1, BigDecimal.ROUND_HALF_UP);
            lastFeedbackMinutes = feedbackDecimal.divide(new BigDecimal(realFirePageLastMonth.size()), 1, BigDecimal.ROUND_HALF_UP);
            lastRunMinutes = runDecimal.divide(new BigDecimal(realFirePageLastMonth.size() * 60), 1, BigDecimal.ROUND_HALF_UP);

            if (BigDecimal.ZERO.compareTo(lastHandleMinutes) != 0) {
                String plainString = thisHandleMinutes.subtract(lastHandleMinutes).divide(lastHandleMinutes, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(0, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
                resultMap.put("handleTimeRate", plainString + "%");
            }
            if (BigDecimal.ZERO.compareTo(lastFeedbackMinutes) != 0) {
                String plainString = thisFeedbackMinutes.subtract(lastFeedbackMinutes).divide(lastFeedbackMinutes, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(0, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
                resultMap.put("feedbackTimeRate", plainString + "%");
            }
            if (BigDecimal.ZERO.compareTo(lastRunMinutes) != 0) {
                String plainString = thisRunMinutes.subtract(lastRunMinutes).divide(lastRunMinutes, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(0, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
                resultMap.put("runTimeRate", plainString + "%");
            }
        }
        // 查询30天误报火警
        Map<String, Object> wrongMap = new HashMap<>();
        wrongMap.put("buildList", buildingIdList);
        Date wrongDate = new Date();
        Date wrongDate1 = DateUtils.addDay(wrongDate, -30);
        wrongMap.put("startTime", DateUtil.formatDate(wrongDate, "yyyy-MM-dd"));
        wrongMap.put("endTime", DateUtil.formatDate(wrongDate1, "yyyy-MM-dd"));
        List<FireInfo> wrongFireList = fireInfoMapper.getWrongFirePage(wrongMap);
        resultMap.put("wrongFireCount", String.valueOf(wrongFireList.size()));
        result.setData(resultMap);
        return result;
    }

    /**
     * 异常信息信息统计
     * @param request
     * @return
     */
    @Override
    public Map<String, String> getFireDoorStatistic(HttpServletRequest request) {
        Map<String, String> result = new HashMap<>();
        Map<String, Object> param = ServletsUtil.getParameters(request);
//        //参数添加起止时间
//        paramAddDate(param);
        // 报警
        int alarm = 0;
        // 故障
        int fault = 0;
        // 离线
        int offLine = 0;

        List<Map<String, Object>> statPumpExpections = firePageWindowMapper.selectAllPumpsExpection(param);
        for (Map<String, Object> statPumpExpection : statPumpExpections) {
            String expectionType = (String) statPumpExpection.get("expectionType");
            if (StringUtils.equals(expectionType, "报警")) {
                alarm++;
            } else if (StringUtils.equals(expectionType, "故障")) {
                fault++;
            } else if (StringUtils.equals(expectionType, "离线")) {
                offLine++;
            }
        }
        if (CollectionUtils.isEmpty(statPumpExpections)) {
            return result;
        }
        String devType = statPumpExpections.get(0).get("devType").toString();

        result.put("devType", devType);
        result.put("alarm", String.valueOf(alarm));
        result.put("fault", String.valueOf(fault));
        result.put("offLine", String.valueOf(offLine));

        return result;
    }

    /**
     * 获取异常统计信息 - 按建筑ID和设备类型分组统计
     *
     * @param request HTTP请求，包含buildingId参数
     * @return 按设备类型分组的异常统计结果
     */
    @Override
    public Map<String, String> getExceptionStatistic(HttpServletRequest request) {
        // 1. 获取并校验参数
        String buildingId = (String) ServletsUtil.getParameters(request).get("buildingId");
        if (StrUtil.isBlank(buildingId)) {
            throw new IllegalArgumentException("建筑ID不能为空");
        }

        // 2. 查询异常数据
        List<StatFireDoorExpection> exceptionList = queryExceptionsByBuildingId(buildingId);

        // 3. 统计异常数据
        ExceptionStatistics statistics = calculateStatistics(exceptionList);

        // 4. 构建返回结果
        return buildResultMap(statistics);
    }

    /**
     * 查询指定建筑的异常数据
     */
    private List<StatFireDoorExpection> queryExceptionsByBuildingId(String buildingId) {
        LambdaQueryWrapper<StatFireDoorExpection> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StatFireDoorExpection::getBuildingId, buildingId)
                   .in(StatFireDoorExpection::getExpectionType, Arrays.asList("1", "2", "3")); // 1报警,2故障,3离线

        return fireDoorExceptionMapper.selectList(queryWrapper);
    }

    /**
     * 统计异常数据
     * devType为0和1时合并统计，devType为2时单独统计
     */
    private ExceptionStatistics calculateStatistics(List<StatFireDoorExpection> exceptionList) {
        Map<String, DeviceStatistic> deviceStatsMap = new HashMap<>();
        TotalStatistic totalStats = new TotalStatistic();

        for (StatFireDoorExpection exception : exceptionList) {
            String deviceType = exception.getDevType();
            String exceptionType = exception.getExpectionType();

            // 根据设备类型决定统计分组
            String statsKey;
            if ("0".equals(deviceType) || "1".equals(deviceType)) {
                // 常开防火门(0)和常闭防火门(1)合并统计
                statsKey = "0,1";
            } else if ("2".equals(deviceType)) {
                // 消防电源监测(2)单独统计
                statsKey = "2";
            } else {
                // 其他类型跳过，不统计
                continue;
            }

            // 获取或创建设备统计
            DeviceStatistic deviceStats = deviceStatsMap.computeIfAbsent(statsKey, k -> new DeviceStatistic());

            // 根据异常类型累加统计
            switch (exceptionType) {
                case "1": // 报警
                    deviceStats.alarmCount++;
                    totalStats.totalAlarm++;
                    break;
                case "2": // 故障
                    deviceStats.faultCount++;
                    totalStats.totalFault++;
                    break;
                case "3": // 离线
                    deviceStats.offlineCount++;
                    totalStats.totalOffline++;
                    break;
            }
        }

        return new ExceptionStatistics(deviceStatsMap, totalStats);
    }

    /**
     * 构建返回结果Map
     */
    private Map<String, String> buildResultMap(ExceptionStatistics statistics) {
        Map<String, String> result = new HashMap<>();

        // 只添加指定设备类型的统计
        for (Map.Entry<String, DeviceStatistic> entry : statistics.deviceStatsMap.entrySet()) {
            String deviceType = entry.getKey();
            DeviceStatistic stats = entry.getValue();

            result.put(deviceType + "_报警", String.valueOf(stats.alarmCount));
            result.put(deviceType + "_故障", String.valueOf(stats.faultCount));
            result.put(deviceType + "_离线", String.valueOf(stats.offlineCount));
        }

        return result;
    }

    /**
     * 设备统计数据
     */
    private static class DeviceStatistic {
        int alarmCount = 0;   // 报警数量
        int faultCount = 0;   // 故障数量
        int offlineCount = 0; // 离线数量
    }

    /**
     * 总计统计数据
     */
    private static class TotalStatistic {
        int totalAlarm = 0;   // 总报警数量
        int totalFault = 0;   // 总故障数量
        int totalOffline = 0; // 总离线数量
    }

    /**
     * 异常统计结果
     */
    private static class ExceptionStatistics {
        final Map<String, DeviceStatistic> deviceStatsMap;
        final TotalStatistic totalStats;

        ExceptionStatistics(Map<String, DeviceStatistic> deviceStatsMap, TotalStatistic totalStats) {
            this.deviceStatsMap = deviceStatsMap;
            this.totalStats = totalStats;
        }
    }

    private Integer getDiffMinutes(String firstTime, String lastTime) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        int diffMinutes = 0;
        try {
            Date firstDate = format.parse(firstTime);
            Date lastDate = format.parse(lastTime);
            diffMinutes = DateUtils.minusMinute(lastDate, firstDate);
        } catch (ParseException e) {
            log.info("计算两个时间差错误，结束时间：{}，开始时间：{}", lastTime, firstTime);
            e.printStackTrace();
        }
        return diffMinutes;
    }
}
