package com.redxun.fire.core.entity.zhongan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 防火门监测设备异常表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("stat_fire_door_expection")
public class StatFireDoorExpection implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.UUID)
    private String id;

    /**
     * 点位号
     */
    @TableField(value = "point_code")
    private String pointCode;

    /**
     * 点位状态
     */
    @TableField(value = "point_status")
    private String pointStatus;


    /**
     * 点位描述
     */
    @TableField(value = "point_desc")
    private String pointDesc;

    /**
     * 设备类型  0-常开防火门, 1-常闭防火门, 2-消防电源监测
     */
    @TableField(value = "dev_type")
    private String devType;

    /**
     * 异常类型（1报警,2故障,3离线,4其他）
     */
    @TableField(value = "expection_type")
    private String expectionType;

    /**
     * 异常上报时间
     */
    @TableField(value = "report_time")
    private String reportTime;

    /**
     * 异常状态（0未处理，1已处理，2暂不处理）
     */
    @TableField(value = "expection_status")
    private String expectionStatus;

    /**
     * 暂不处理结束时间
     */
    @TableField(value = "end_time")
    private String endTime;

    /**
     * 暂不处理原因
     */
    @TableField(value = "reason")
    private String reason;

    /**
     * 建筑物id
     */
    @TableField(value = "building_id")
    private String buildingId;

    /**
     * 处理情况
     */
    @TableField(value = "handling_info")
    private String handlingInfo;

    /**
     * 暂不处理天
     */
    @TableField(value = "day")
    private String day;

    /**
     * 暂不处理小时
     */
    @TableField(value = "hour")
    private String hour;

    /**
     * 点位id
     */
    @TableField(value = "point_id")
    private String pointId;

    /**
     * 最后上报时间
     */
    @TableField(value = "report_end_time")
    private String reportEndTime;

    /**
     * 处理人时间
     */
    @TableField(value = "handling_time")
    private String handlingTime;

    /**
     * 处理人id
     */
    @TableField(value = "handling_id")
    private String handlingId;

    /**
     * 处理人姓名
     */
    @TableField(value = "handling_name")
    private String handlingName;

    /**
     * 所属部门的组织机构id
     */
    @TableField(value = "belong_dep")
    private String belongDep;

    /**
     * 是否发送监控台(0未发送,1已发送)
     */
    @TableField(value = "is_send")
    private String isSend;

    /**
     * 审批状态（0 未审批，1 审批通过，2 审批拒绝）
     */
    @TableField(value = "approve_status")
    private String approveStatus;

    /**
     * 审批时间
     */
    @TableField(value = "approve_time")
    private String approveTime;

    /**
     * 消息推送历史类型(只记录最新一次记录 1,2,3) 对应config_relation.sort
     */
    @TableField(value = "send_history_type")
    private Integer sendHistoryType;

    /**
     * 报警次数
     */
    @TableField(value = "times")
    private Integer times;

    /**
     * 反馈图片
     */
    @TableField(value = "pic_urls")
    private String picUrls;

}
