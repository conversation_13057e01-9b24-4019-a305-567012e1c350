# 增加防火门、消防监测主机品牌型号
INSERT INTO `wd_iot_fire`.`brand_model` (`id`, `brand_name`, `CREATE_DEP_ID_`, `CREATE_BY_`, `CREATE_TIME_`, `UPDATE_BY_`, `UPDATE_TIME_`) VALUES ('202507281', '北京利达/LD-FM118', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wd_iot_fire`.`brand_model` (`id`, `brand_name`, `CREATE_DEP_ID_`, `CREATE_BY_`, `CREATE_TIME_`, `UPDATE_BY_`, `UPDATE_TIME_`) VALUES ('202507282', '泰和安/TM3500', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wd_iot_fire`.`brand_model` (`id`, `brand_name`, `CREATE_DEP_ID_`, `CREATE_BY_`, `CREATE_TIME_`, `UPDATE_BY_`, `UPDATE_TIME_`) VALUES ('202507283', '海湾/GST-DJ-N500', NULL, NULL, NULL, NULL, NULL);


# 新增主机设备类型
INSERT INTO drop_down (DIC_ID_, TREE_ID_, NAME_, VALUE_, DESCP_)
VALUES ('1752132168007', '95516', '防火门监控主机', 'FHMJKZJ', '设备类型');
VALUES ('1752132169011', '95516', '消防电源监测主机', 'XFDYJCZJ', '设备类型');

# 新增点位设备设备类型-常开常闭防火门-消防电源监测
INSERT INTO `wd_iot_fire`.`wd_dev_config` (`id`, `dev_code`, `dev_name`, `fire_sys_code`, `parent_code`, `dev_level`, `dev_category`, `sort`, `enabled`, `create_user`, `create_time`, `modify_user`, `modify_time`, `police_name`, `police_type`, `police_id`, `dev_type`) VALUES ('1752039146413', 'CKFHM', '常开防火门', '13', '0', 1, '2', 0, 0, '1', '2020-12-28 16:48:19', '1', '2020-12-28 16:48:19', '监管', '0', '95520', NULL);
INSERT INTO `wd_iot_fire`.`wd_dev_config` (`id`, `dev_code`, `dev_name`, `fire_sys_code`, `parent_code`, `dev_level`, `dev_category`, `sort`, `enabled`, `create_user`, `create_time`, `modify_user`, `modify_time`, `police_name`, `police_type`, `police_id`, `dev_type`) VALUES ('1752039412350', 'CBFHM', '常闭防火门', '13', '0', 1, '2', 0, 0, '1', '2025-07-09 13:37:15', '1', '2025-07-09 13:37:15', '监管', '0', '95520', NULL);
INSERT INTO `wd_iot_fire`.`wd_dev_config` (`id`, `dev_code`, `dev_name`, `fire_sys_code`, `parent_code`, `dev_level`, `dev_category`, `sort`, `enabled`, `create_user`, `create_time`, `modify_user`, `modify_time`, `police_name`, `police_type`, `police_id`, `dev_type`) VALUES ('1752563437618', 'XFDYJC', '消防电源监测', '18', '0', 1, '2', 1, 0, '1', '2025-07-15 17:26:08', '1', '2025-07-15 17:26:08', '监管', '5', '95520', NULL);

# 新增
# 消防消防系统  防火门及卷帘系统  常开防火门
# 消防系统  防火门及卷帘系统  常闭防火门
# 消防系统  消防物联网监测系统  消防电源监测
INSERT INTO `wd_iot_fire`.`wd_fire_sys` (`id`, `fire_sys_code`, `fire_sys_name`, `sys_category`, `sort`, `enabled`, `create_user`, `create_time`, `modify_user`, `modify_time`) VALUES ('13', '13', '防火门及卷帘系统', '1', 13, 0, '1', '2020-09-28 14:41:28', '1', '2020-09-28 14:41:28');
INSERT INTO `wd_iot_fire`.`wd_fire_sys` (`id`, `fire_sys_code`, `fire_sys_name`, `sys_category`, `sort`, `enabled`, `create_user`, `create_time`, `modify_user`, `modify_time`) VALUES ('101', '18', '消防物联网监测系统  ', '1', 0, 0, '1', '2025-07-30 10:08:13', '1', '2025-07-30 10:08:18');

# 新增防火门异常表
create table stat_fire_door_expection
(
    id                varchar(32)      not null comment '主键'
        primary key,
    point_code        varchar(32)      null comment '点位号',
    point_status      varchar(100)     null comment '点位状态',
    point_desc        varchar(100)     null comment '点位描述',
    dev_type          varchar(2)       null comment '设备类型: 0-常开防火门, 1-常闭防火门, 2-消防电源监测',
    expection_type    char             null comment '异常类型（1异常报警,2设备故障,3设备离线，4其他）',
    report_time       varchar(32)      null comment '异常上报时间',
    expection_status  char             null comment '异常状态（0未处理，1已处理，2暂不处理）',
    end_time          varchar(32)      null comment '暂不处理结束时间',
    reason            varchar(1000)    null comment '暂不处理原因',
    building_id       varchar(32)      not null comment '建筑物id',
    handling_info     varchar(1000)    null comment '处理情况',
    day               varchar(2)       null comment '暂不处理天',
    hour              varchar(2)       null comment '暂不处理小时',
    point_id          varchar(100)     null comment '点位id',
    report_end_time   varchar(32)      null comment '最后上报时间',
    handling_time     varchar(100)     null comment '处理人时间',
    handling_id       varchar(100)     null comment '处理人id',
    handling_name     varchar(100)     null comment '处理人姓名',
    belong_dep        varchar(100)     null comment '所属部门的组织机构id',
    is_send           char default '0' null comment '是否发送监控台(0未发送,1已发送)',
    approve_status    char default '0' null comment '审批状态（0 未审批，1 审批通过，2 审批拒绝）',
    approve_time      varchar(32)      null comment '审批时间',
    send_history_type int  default 0   null comment '消息推送历史类型(只记录最新一次记录 1,2,3) 对应config_relation.sort',
    pic_urls          varchar(1024)    null comment '反馈图片',
    times             int  default 1   null comment '报警次数'
)
    comment '防火门设备异常表' collate = utf8mb4_general_ci
                                 row_format = DYNAMIC;


# 新增防火门点位数量统计表
create table fire_door_real
(
    id                  bigint        not null comment '主键'
        primary key,
    building_id         varchar(32)   null comment '建筑物id',
    building_name       varchar(100)  null comment '建筑名称',
    point_device_type   varchar(10)   null comment '检测点位设备类型(防火门监控主机 0、消防电源监测主机 1)',
    point_count         int default 0 null comment '检测点位数量',
    normal_count        int default 0 null comment '正常点位总数量',
    alarm_count         int default 0 null comment '报警点位',
    fault_count         int default 0 null comment '故障点位',
    offline_count       int default 0 null comment '离线点位总数量'
)
    comment '防火门监测设备点位数量广场统计' collate = utf8mb4_general_ci
                                               row_format = DYNAMIC;

# 测试数据
-- 防火门监测设备点位数量统计表 - 测试数据

INSERT INTO fire_door_real (id, building_id, building_name, point_device_type, point_count, normal_count, alarm_count, fault_count, offline_count) VALUES
(1, 'WD001', '万达广场A座', '0', 120, 95, 8, 12, 5),
(2, 'WD002', '万达广场B座', '0', 85, 70, 5, 7, 3),
(3, 'WD003', '万达广场C座', '1', 150, 125, 12, 8, 5),
(4, 'WD004', '万达广场D座', '0', 200, 180, 6, 10, 4),
(5, 'WD005', '万达广场E座', '1', 90, 75, 4, 8, 3),
(6, 'WD006', '万达广场F座', '0', 110, 95, 7, 5, 3);

