package com.redxun.fire.core.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.redxun.common.base.entity.JsonPageResult;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.entity.BaseDevicePoint;
import com.redxun.fire.core.pojo.base.ResultMsg;

import com.redxun.fire.core.service.alarm.IFirePageWindowService;
import com.redxun.fire.core.service.building.IBaseBuildingFloorService;
import com.redxun.fire.core.service.common.impl.DropDownServiceImpl;
import com.redxun.fire.core.service.device.IBaseDevicePointService;
import com.redxun.fire.core.service.zhongan.FireDoorMonitoringService;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 防火门-消防电源监测
 */
@RestController
@RequestMapping(value = "/fireDoorMonitoring")
public class FireDoorMonitoringController {

    @Autowired
    private FireDoorMonitoringService fireDoorMonitoringService;

    @Resource
    private IFirePageWindowService firePageWindowService;

    @Resource
    IBaseDevicePointService baseDevicePointService;

    @Resource
    private IBaseBuildingFloorService iBaseBuildingFloorService;

    @Autowired
    private DropDownServiceImpl dropDownService;



    /**
     * 设备监测广场统计
     * 支持参数：
     * - pointDeviceType: 检测点位设备类型(防火门监控主机 0、消防电源监测主机 1)
     */
    @PostMapping(value = "/squareStatistics")
    public JsonPageResult squareStatistics(HttpServletRequest req, @RequestBody QueryData queryData) {
        return fireDoorMonitoringService.squareStatistics(req, queryData);
    }

    /**
     * 获取某个建筑下的点位设备列表
     */
    @PostMapping(value = "/list/{buildingId}")
    public JsonPageResult queryDeviceList(@PathVariable("buildingId") String buildingId,
                                         @RequestBody QueryData queryData) {
        return fireDoorMonitoringService.queryDeviceList(buildingId, queryData);
    }

    /**
     * 统计指定建筑下各设备类型的总量、报警、离线、异常、正常数量等，并统计点位数量。
     * 支持参数：
     * - pointDeviceType: 检测点位设备类型(防火门监控主机 0、消防电源监测主机 1)
     * - devTypeName: 设备类型名称（兼容旧版本）
     * @param buildingId 建筑物ID
     * @param queryData 查询参数，可包含设备类型筛选条件
     * @return JsonResult 统计结果
     */
    @PostMapping(value = "/statistics/{buildingId}")
    public JsonResult statistics(@PathVariable("buildingId") String buildingId,
                                @RequestBody(required = false) QueryData queryData) {
        return fireDoorMonitoringService.statistics(buildingId, queryData);
    }

    /**
     * 设备异常列表
     */
    @PostMapping(value = "/getElectricalFireAzlarmList/{buildingId}")
    public JsonPageResult getElectricalFireAlarmList(@PathVariable("buildingId") String buildingId, @RequestBody QueryData queryData) {
        return fireDoorMonitoringService.getElectricalFireAlarmList(buildingId, queryData);
    }

    /**
     * 异常数量统计
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getExceptionStatistic", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getPumpStatistic(HttpServletRequest request) {
        return ResultMsg.getResultMsg("获取异常数量统计信息成功", firePageWindowService.getExceptionStatistic(request), 200);
    }

    /**
     * 设备异常处理
     */
    @PostMapping(value = "/FireAlarmHandle")
    public JsonResult FireAlarmHandle(@RequestParam("exceptionId") String exceptionId,
                                     @RequestBody Map<String, String> requestData,
                                     HttpServletRequest request) {
        return fireDoorMonitoringService.editFireDevice(exceptionId, requestData, request);
    }

    /**
     * 点位根据id查询
     */
    @ResponseBody
    @GetMapping("queryPointById")
    public JsonResult<BaseDevicePoint> queryPointById(String id) {
        if (!StringUtils.isEmpty(id)) {
            final val point = baseDevicePointService.getById(id);
            if (ObjectUtil.isNotEmpty(point) && StrUtil.isNotBlank(point.getFloorId())) {
                final val floor = iBaseBuildingFloorService.getBaseMapper().selectById(point.getFloorId());
                if (ObjectUtil.isNotEmpty(floor)) {
                    point.setWztFloorId(floor.getWztFloorId());
                    point.setWztFloorName(floor.getWztFloorName());
                    point.setWztBuilding(floor.getWztConstructId());
                    point.setWztBuildingName(floor.getWztConstructName());
                }
            }
            return new JsonResult<BaseDevicePoint>().setData(point);
        } else {
            return new JsonResult<BaseDevicePoint>().setData(null);
        }
    }

    /**
     * 查询设备类型下拉选项
     * 根据TREE_ID_查询设备类型95516
     * @param treeId 分类ID
     */
    @GetMapping("queryDeviceTypeDropDown/{treeId}")
    public JsonResult queryDeviceTypeDropDown(@PathVariable("treeId") String treeId) {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(dropDownService.queryNameByTreeId(treeId));
        return jsonResult;
    }

    /**
     * 根据建筑物信息和防火门异常信息进行联动查询
     * @return JsonPageResult 返回建筑物和防火门异常的联动查询结果
     */
    @PostMapping(value = "/buildingFireDoorQuery")
    public JsonPageResult buildingFireDoorQuery(@RequestBody QueryData queryData) {
        return fireDoorMonitoringService.buildingFireDoorQuery(queryData);
    }


}
