package com.redxun.fire.core.service.zhongan.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.redxun.common.base.entity.JsonPageResult;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.fire.core.entity.BaseDevicePoint;
import com.redxun.fire.core.entity.BaseHostInfo;
import com.redxun.fire.core.entity.FarEastoneCache;
import com.redxun.fire.core.entity.FireInfo;
import com.redxun.fire.core.entity.wzt.UsersResponse;
import com.redxun.fire.core.entity.zhongan.FireDoorReal;
import com.redxun.fire.core.entity.zhongan.StatFireDoorExpection;
import com.redxun.fire.core.mapper.*;
import com.redxun.fire.core.service.user.IUserDataPowerService;
import com.redxun.fire.core.service.user.impl.OrgMiddleServiceImpl;
import com.redxun.fire.core.service.zhongan.FireDoorMonitoringService;
import com.redxun.fire.core.utils.FastJSONUtils;
import com.redxun.fire.core.utils.RedisUtils;
import com.xxl.job.core.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FireDoorMonitoringServiceImpl implements FireDoorMonitoringService {
    @Autowired
    private FireDoorRealMapper fireDoorRealMapper;
    @Autowired
    private IUserDataPowerService   userDataPowerService;
    @Autowired
    private BaseHostInfoMapper baseHostInfoMapper;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private BaseDevicePointMapper baseDevicePointMapper;
    @Autowired
    private FireInfoMapper fireInfoMapper;
    @Autowired
    private FireDoorExceptionMapper fireDoorExceptionMapper;
    @Autowired
    private OrgMiddleServiceImpl orgMiddleService;




    // 远程主机
    private static final String REDIS_KEY_PREFIX = "far_eastone:";
    // 点位
    public static final String REDIS_KEY_POINT= "point_status:";

    /**
     * 从查询参数中获取 pointDeviceType 参数
     * @param queryData 查询参数
     * @return pointDeviceType 值，如果不存在则返回 null
     */
    private String getPointDeviceTypeFromQuery(QueryData queryData) {
        if (queryData == null || queryData.getParams() == null) {
            return null;
        }
        return queryData.getParams().get("pointDeviceType");
    }



    /**
     * 设备监测广场统计
     */
    @Override
    public JsonPageResult squareStatistics(HttpServletRequest request, QueryData queryData) {
        String userId = request.getHeader("Wzt-Userid");
        // 获得所有的用户权限的广场ID
        List<String> userDataPower = userDataPowerService.findUserDataPower(userId);
        log.info("[防火门设备列表] 获取用户权限的广场ID: {}", userDataPower);

        // 设备类型
        String pointDeviceType = getPointDeviceTypeFromQuery(queryData);
        log.info("[防火门设备列表] 获取点位设备类型: {}", pointDeviceType);

        // 构建分页查询
        Page <FireDoorReal> page = new Page<>(queryData.getPageNo(), queryData.getPageSize());
        Page<FireDoorReal> realList = fireDoorRealMapper.getFireRealList(page, userDataPower, pointDeviceType);
        return new JsonPageResult(realList);
    }

    /**
     * 获取某个建筑下的点位设备列表
     *
     * @param buildingId 建筑物ID
     * @param queryData 查询参数
     * @return 分页查询结果
     */
    @Override
    public JsonPageResult queryDeviceList(String buildingId, QueryData queryData) {
        log.info("[防火门设备列表] 开始查询，建筑ID: {}", buildingId);

        try {
            // 查询并验证主机信息
            List<String> hostIds = getFireDoorHostIds(buildingId, queryData);
            if (hostIds.isEmpty()) {
                log.info("[防火门设备列表] 建筑[{}]下未找到防火门主机", buildingId);
                return new JsonPageResult(new Page<>());
            }

            // 构建设备点位查询条件
            LambdaQueryWrapper<BaseDevicePoint> queryWrapper = buildFireDoorDevicePointQuery(buildingId, hostIds, queryData);

            // 执行分页查询
            Page<BaseDevicePoint> page = new Page<>(queryData.getPageNo(), queryData.getPageSize());
            IPage<BaseDevicePoint> devicePointPage = baseDevicePointMapper.selectPage(page, queryWrapper);

            // 填充设备实时状态信息
            enrichDevicePointsWithRealTimeStatus(devicePointPage.getRecords());

            log.info("[防火门设备列表] 查询完成，返回{}条记录", devicePointPage.getRecords().size());
            return new JsonPageResult(devicePointPage);

        } catch (Exception e) {
            log.error("[防火门设备列表] 查询异常，建筑ID: {}", buildingId, e);
            throw new RuntimeException("查询防火门设备列表失败", e);
        }
    }

    /**
     * 获取防火门主机ID列表
     *
     * @param buildingId 建筑物ID
     * @param queryData 查询参数
     * @return 主机ID列表
     */
    private List<String> getFireDoorHostIds(String buildingId, QueryData queryData) {
        log.info("[获取主机ID] 开始查询建筑[{}]下的防火门主机", buildingId);

        // 设置查询参数
        queryData.getParams().put("buildingId", buildingId);

        // 查询主机信息
        List<BaseHostInfo> hostInfos = baseHostInfoMapper.selectList(getQueryWrapper(queryData));

        // 提取主机ID列表
        List<String> hostIds = hostInfos.stream().map(BaseHostInfo::getHid).collect(Collectors.toList());

        log.info("[获取主机ID] 建筑[{}]下找到{}台主机", buildingId, hostIds.size());
        return hostIds;
    }

    /**
     * 构建设备点位查询条件
     *
     * @param buildingId 建筑物ID
     * @param hostIds 主机ID列表
     * @param queryData 查询参数
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<BaseDevicePoint> buildFireDoorDevicePointQuery(String buildingId,
                                                                              List<String> hostIds,
                                                                              QueryData queryData) {
        log.info("[构建查询条件] 建筑ID: {}, 主机数量: {}", buildingId, hostIds.size());

        LambdaQueryWrapper<BaseDevicePoint> queryWrapper = new LambdaQueryWrapper<>();

        // 基础条件：主机ID范围
        queryWrapper.in(BaseDevicePoint::getHostId, hostIds);

        // 基础条件：建筑ID
        if (StrUtil.isNotBlank(buildingId)) {
            queryWrapper.eq(BaseDevicePoint::getBuildingId, buildingId);
        }

        // 可选条件：设备类型名称（模糊查询）
        String devTypeName = queryData.getParams().get("devTypeName");
        if (StrUtil.isNotBlank(devTypeName)) {
            queryWrapper.like(BaseDevicePoint::getDevTypeName, devTypeName);
            log.info("[构建查询条件] 添加设备类型模糊查询: {}", devTypeName);
        }

        // 可选条件：楼层ID
        String floorId = queryData.getParams().get("floorId");
        if (StrUtil.isNotBlank(floorId)) {
            queryWrapper.eq(BaseDevicePoint::getFloorId, floorId);
            log.info("[构建查询条件] 添加楼层筛选: {}", floorId);
        }

        // 可选条件：设备编号（模糊查询）
        String did = queryData.getParams().get("did");
        if (StrUtil.isNotBlank(did)) {
            queryWrapper.like(BaseDevicePoint::getDid, did);
            log.info("[构建查询条件] 添加设备编号筛选: {}", did);
        }

        return queryWrapper;
    }

    /**
     * 为设备点位填充实时状态信息
     *
     * @param devicePoints 设备点位列表
     */
    private void enrichDevicePointsWithRealTimeStatus(List<BaseDevicePoint> devicePoints) {
        if (devicePoints == null || devicePoints.isEmpty()) {
            log.info("[填充状态信息] 设备点位列表为空，跳过状态填充");
            return;
        }

        log.info("[填充状态信息] 开始为{}个设备点位填充实时状态", devicePoints.size());

        for (BaseDevicePoint devicePoint : devicePoints) {
            try {
                // 从Redis获取设备实时状态
                FarEastoneCache alarmCache = PointFromRedis(devicePoint.getBuildingId(), devicePoint.getId());

                if (alarmCache != null) {
                    // 填充基础状态信息
                    devicePoint.setAlarmStatus(String.valueOf(alarmCache.getAlarmStatus()));
                    devicePoint.setFaultStatus(alarmCache.getFaultStatus());
                    devicePoint.setDeviceStatus(alarmCache.getDeviceStatus());
                    devicePoint.setHeartbeatTime(alarmCache.getPushTime());

                    log.trace("[填充状态信息] 设备[{}] 报警状态: {}, 设备状态: {}, 故障状态: {}",
                            devicePoint.getDid(), devicePoint.getAlarmStatus(), devicePoint.getDeviceStatus(), devicePoint.getFaultStatus());
                } else {
                    // 未找到缓存数据，置空相关字段
                    devicePoint.setAlarmStatus(null);
                    devicePoint.setDeviceStatus(null);
                    devicePoint.setFaultStatus(null);
                    devicePoint.setHeartbeatTime(null);

                    log.trace("[填充状态信息] 设备[{}]未找到缓存数据，字段置空", devicePoint.getDid());
                }
            } catch (Exception e) {
                log.warn("[填充状态信息] 设备[{}]状态填充异常: {}", devicePoint.getDid(), e.getMessage());
                // 异常情况下置空相关字段
                devicePoint.setAlarmStatus(null);
                devicePoint.setDeviceStatus(null);
                devicePoint.setFaultStatus(null);
                devicePoint.setHeartbeatTime(null);
            }
        }

        log.info("[填充状态信息] 状态填充完成，处理了{}个设备点位", devicePoints.size());
    }



    /**
     * 统计指定建筑下各设备类型的总量、报警、离线、异常、正常数量，并统计点位数量。
     * @param buildingId 建筑ID
     * @return 统计结果，按设备类型分组
     */
    @Override
    public JsonResult statistics(String buildingId) {
        // 调用重载方法，传入null表示使用默认查询条件
        return statistics(buildingId, null);
    }

    /**
     * 统计指定建筑下各设备类型的总量、报警、离线、异常、正常数量，并统计点位数量。
     * 支持根据设备类型筛选
     *
     * @param buildingId 建筑ID
     * @param queryData 查询参数，可包含设备类型筛选条件
     * @return 统计结果，按设备类型分组
     */
    @Override
    public JsonResult statistics(String buildingId, QueryData queryData) {
        log.info("[statistics] 入参 buildingId={}", buildingId);

        if (isInvalidBuildingId(buildingId)) {
            log.warn("[statistics] 参数错误，buildingId为空");
            return JsonResult.Fail("参数错误");
        }

        // 初始化查询参数
        if (queryData == null) {
            queryData = new QueryData();
        }

        // 1. 获取防火门主机ID列表（支持设备类型筛选）
        List<String> hostIds = getFireDoorHostIds(buildingId, queryData);
        if (hostIds.isEmpty()) {
            log.info("[statistics] 建筑[{}]下未找到符合条件的主机", buildingId);
            return JsonResult.getSuccessResult(new ArrayList<>());
        }

        log.info("[statistics] 找到{}台符合条件的主机", hostIds.size());

        // 2. 获取主机缓存信息
        Map<String, FarEastoneCache> hostCacheMap = batchGetGasAlarmCaches(hostIds);

        // 如果缓存为空，直接返回默认统计数据
        if (hostCacheMap.isEmpty()) {
            log.info("[statistics] Redis缓存为空，返回默认统计数据");
            return JsonResult.getSuccessResult(getDefaultStatsData());
        }

        // 3. 按设备类型分组并统计（根据前端参数精准统计）
        List<Map<String, Object>> deviceTypeStatsList = buildDeviceTypeStats(hostCacheMap, queryData);

        log.info("[statistics] 统计结果: {}", deviceTypeStatsList);
        return JsonResult.getSuccessResult(deviceTypeStatsList);
    }

    private LambdaQueryWrapper<BaseHostInfo> getQueryWrapper(QueryData queryData) {
        LambdaQueryWrapper<BaseHostInfo> in = new LambdaQueryWrapper<>();
        in.isNotNull(BaseHostInfo::getDeviceType);
        if(queryData.getParams().containsKey("buildingName") && StrUtil.isNotBlank(queryData.getParams().get("buildingName"))){
            in.like(BaseHostInfo::getBuildingName,queryData.getParams().get("buildingName"));
        }
        if(queryData.getParams().containsKey("buildingId") && StrUtil.isNotBlank(queryData.getParams().get("buildingId"))){
            in.like(BaseHostInfo::getBuildingId,queryData.getParams().get("buildingId"));
        }
        if(queryData.getParams().containsKey("hostModelName") && StrUtil.isNotBlank(queryData.getParams().get("hostModelName"))){
            in.like(BaseHostInfo::getHostModelName,queryData.getParams().get("hostModelName"));
        }
        if(queryData.getParams().containsKey("terminalBrandName") && StrUtil.isNotBlank(queryData.getParams().get("terminalBrandName"))){
            in.like(BaseHostInfo::getTerminalBrandName,queryData.getParams().get("terminalBrandName"));
        }
        if(queryData.getParams().containsKey("deviceType") && StrUtil.isNotBlank(queryData.getParams().get("deviceType"))){
            in.like(BaseHostInfo::getDeviceType,queryData.getParams().get("deviceType"));
        }
        return in;
    }

    private Map<String, FarEastoneCache> batchGetGasAlarmCaches(List<String> hostIds) {
        List<Object> results = redisUtils.batchGet(hostIds.stream().map(id -> REDIS_KEY_PREFIX + id).collect(Collectors.toList()));
        if(results.isEmpty()){
            return Maps.newHashMap();
        }
        Map<String, FarEastoneCache> cacheMap = new HashMap<>();
        for (int i = 0; i < hostIds.size(); i++) {
            String hostId = hostIds.get(i);
            Object result = results.get(i);
            if (result != null) {
                cacheMap.put(hostId, FastJSONUtils.toBean(result.toString(), FarEastoneCache.class));
            }
        }
        return cacheMap;
    }

    /**
     * 获取默认统计数据
     */
    private List<Map<String, Object>> getDefaultStatsData() {
        List<Map<String, Object>> defaultStatsList = new ArrayList<>();

        // 防火门监控主机默认统计
        Map<String, Object> fireDoorStats = new HashMap<>();
        fireDoorStats.put("deviceType", "缓存不存在、返回默认值");
        fireDoorStats.put("total", 0);
        fireDoorStats.put("alarm", 0);
        fireDoorStats.put("offline", 0);
        fireDoorStats.put("abnormal", 0);
        fireDoorStats.put("normal", 0);
        defaultStatsList.add(fireDoorStats);

        log.info("[获取默认统计数据] 返回默认统计结果");
        return defaultStatsList;
    }

    private void countPoint(Map<String, Object> result,String buildingId,List<String> hostIds) {
        int abnormal = 0;
        if(hostIds.isEmpty()){
            result.put("pointTotal", 0);
            result.put("pointAbnormal", abnormal);
        }else{
            //统计所有的点位
            List<BaseDevicePoint> points = baseDevicePointMapper.selectList(new LambdaQueryWrapper<BaseDevicePoint>()
                                                                                    .in(BaseDevicePoint::getHostId, hostIds).eq(BaseDevicePoint::getBuildingId, buildingId));
            List<String> stringList = points.stream().map(BaseDevicePoint::getId).collect(Collectors.toList());

            if(!stringList.isEmpty()){
                //统计所有的异常点位
                abnormal = fireInfoMapper.selectList(new LambdaQueryWrapper<FireInfo>().in(FireInfo::getPointId, stringList)
                                                                                       .eq(FireInfo::getFireStatus, "0")).size();
            }
            result.put("pointTotal", points.size());
            result.put("pointAbnormal", abnormal);
        }

    }

    /** 按设备类型分组并统计各类数量（支持精准筛选） */
    private List<Map<String, Object>> buildDeviceTypeStats(Map<String, FarEastoneCache> hostCacheMap, QueryData queryData) {
        Map<String, List<FarEastoneCache>> grouped = groupByDeviceType(hostCacheMap);
        List<Map<String, Object>> statsList = new ArrayList<>();

        // 获取前端指定的设备类型
        String devTypeName = queryData.getParams().get("devTypeName");

        if (StrUtil.isNotBlank(devTypeName)) {
            // 前端指定了设备类型，只统计指定的类型
            if (devTypeName.contains(",")) {
                // 多个设备类型
                String[] deviceTypes = devTypeName.split(",");
                for (String deviceType : deviceTypes) {
                    deviceType = deviceType.trim();
                    if (grouped.containsKey(deviceType)) {
                        statsList.add(calculateStats(grouped.get(deviceType), deviceType));
                        log.info("[buildDeviceTypeStats] 统计设备类型: {}", deviceType);
                    }
                }
            } else {
                // 单个设备类型
                devTypeName = devTypeName.trim();
                if (grouped.containsKey(devTypeName)) {
                    statsList.add(calculateStats(grouped.get(devTypeName), devTypeName));
                    log.info("[buildDeviceTypeStats] 统计设备类型: {}", devTypeName);
                }
            }
        } else {
            // 前端没有指定设备类型，统计所有类型（默认行为）
            for (Map.Entry<String, List<FarEastoneCache>> entry : grouped.entrySet()) {
                statsList.add(calculateStats(entry.getValue(), entry.getKey()));
            }
            log.info("[buildDeviceTypeStats] 统计所有设备类型");
        }

        return statsList;
    }


    /** 查询主机ID列表 */
    private List<String> getHostIdsByBuilding(String buildingId, QueryData queryData) {
        if (StrUtil.isBlank(buildingId)) {
            throw new IllegalArgumentException("buildingId不能为空");
        }
        queryData.getParams().put("buildingId", buildingId);
        return baseHostInfoMapper.selectList(getQueryWrapper(queryData)).stream()
                .map(BaseHostInfo::getHid)
                .collect(Collectors.toList());
    }

    /** 构建点位查询条件 */
    private LambdaQueryWrapper<BaseDevicePoint> buildDevicePointQuery(String buildingId, List<String> hostIds, QueryData queryData) {
        LambdaQueryWrapper<BaseDevicePoint> query = new LambdaQueryWrapper<>();
        query.in(BaseDevicePoint::getHostId, hostIds);

        // 优先用前端传递的devTypeName，没有则用默认值
        String devTypeName = queryData.getParams().get("devTypeName") != null ? queryData.getParams().get("devTypeName") : "防火门监控主机";
        query.eq(BaseDevicePoint::getDevTypeName, devTypeName);

        if (StrUtil.isNotBlank(buildingId)) {
            query.eq(BaseDevicePoint::getBuildingId, buildingId);
        }
        if (StrUtil.isNotBlank(queryData.getParams().get("floorId"))) {
            query.eq(BaseDevicePoint::getFloorId, queryData.getParams().get("floorId"));
        }
        if (StrUtil.isNotBlank(queryData.getParams().get("did"))) {
            query.like(BaseDevicePoint::getDid, queryData.getParams().get("did"));
        }
        if (StrUtil.isNotBlank(queryData.getParams().get("devTypeName"))) {
            query.like(BaseDevicePoint::getPointName, queryData.getParams().get("devTypeName"));
        }
        return query;
    }

    /** 设置点位的实时状态 */
    private void setDevicePointStatusFromRedis(BaseDevicePoint devicePoint) {
        FarEastoneCache alarmCache = PointFromRedis(devicePoint.getBuildingId(), devicePoint.getId());
        if (alarmCache != null) {
            // 报警
            devicePoint.setAlarmStatus(String.valueOf(alarmCache.getAlarmStatus()));
            // 故障
            devicePoint.setFaultStatus(alarmCache.getFaultStatus());
            // 离线
            devicePoint.setDeviceStatus(alarmCache.getDeviceStatus());
            // 心跳时间
            devicePoint.setHeartbeatTime(alarmCache.getLastHeartbeatTime());

        }
    }

    private FarEastoneCache PointFromRedis(String buildingId,String pointId) {
        String redisKey = REDIS_KEY_POINT + buildingId+"-"+pointId;
        Object object = redisUtils.get(redisKey);
        if (object == null) {
            return null;
        }
        return FastJSONUtils.toBean(object.toString(), FarEastoneCache.class);
    }

    // 参数校验
    private boolean isInvalidBuildingId(String buildingId) {
        return StrUtil.isBlank(buildingId);
    }

    // 查询主机信息
    private List<BaseHostInfo> getBaseHostInfos(String buildingId) {
        QueryData queryData = new QueryData();
        queryData.getParams().put("buildingId", buildingId);
        return baseHostInfoMapper.selectList(getQueryWrapper(queryData));
    }

    // 提取主机ID
    private List<String> extractHostIds(List<BaseHostInfo> baseHostInfos) {
        return baseHostInfos.stream().map(BaseHostInfo::getHid).collect(Collectors.toList());
    }

    // 按设备类型分组
    private Map<String, List<FarEastoneCache>> groupByDeviceType(Map<String, FarEastoneCache> cacheMap) {
        return cacheMap.values().stream()
            .filter(cache -> cache.getDeviceType() != null)
            .collect(Collectors.groupingBy(FarEastoneCache::getDeviceType));
    }

    // 统计单个设备类型
    private Map<String, Object> calculateStats(List<FarEastoneCache> caches, String deviceType) {
        int total = caches.size();
        int offline = (int) caches.stream().filter(c -> "1".equals(c.getFireDoorOfflineStatus())).count();
        int abnormal = (int) caches.stream().filter(c -> "1".equals(c.getFireDoorFaultStatus())).count();
        int alarm = (int) caches.stream().filter(c -> "1".equals(c.getFireDoorAlarmStatus())).count();
        int normal = total - offline - abnormal - alarm;
        Map<String, Object> stats = new HashMap<>();
        stats.put("deviceType", deviceType);
        stats.put("total", total);
        stats.put("alarm", alarm);
        stats.put("offline", offline);
        stats.put("abnormal", abnormal);
        stats.put("normal", normal);
        return stats;
    }

    /**
     * 获取防火门异常列表
     *
     * @param buildingId 建筑物ID（必填）
     * @param queryData 查询参数，包含：
     *                  - devType: 设备类型（可选）
     *                  - expectionType: 异常类型（可选，1报警,2故障,3离线,4其他）
     *                  - pageNo: 页码
     *                  - pageSize: 每页大小
     * @return 分页查询结果
     * @throws IllegalArgumentException 当参数校验失败时抛出
     */
    @Override
    public JsonPageResult getElectricalFireAlarmList(String buildingId, QueryData queryData) {
        log.info("[防火门异常列表] 开始查询，建筑ID: {}", buildingId);

        try {
            // 1. 参数校验
            validateQueryParameters(buildingId, queryData);

            // 2. 构建查询条件
            LambdaQueryWrapper<StatFireDoorExpection> queryWrapper = buildExceptionQueryWrapper(buildingId, queryData.getParams());

            // 3. 执行分页查询
            Page<StatFireDoorExpection> page = new Page<>(queryData.getPageNo(), queryData.getPageSize());
            Page<StatFireDoorExpection> resultPage = fireDoorExceptionMapper.selectPage(page, queryWrapper);

            log.info("[防火门异常列表] 查询完成，返回{}条记录", resultPage.getRecords().size());
            return new JsonPageResult(resultPage);

        } catch (Exception e) {
            log.error("[防火门异常列表] 查询异常，建筑ID: {}", buildingId, e);
            throw e;
        }
    }

    /**
     * 校验查询参数
     *
     * @param buildingId 建筑物ID
     * @param queryData 查询参数
     * @throws IllegalArgumentException 当参数校验失败时抛出异常
     */
    private void validateQueryParameters(String buildingId, QueryData queryData) {
        if (StrUtil.isBlank(buildingId)) {
            log.error("[防火门异常列表] 建筑ID不能为空");
            throw new IllegalArgumentException("建筑ID不能为空");
        }

        if (queryData == null) {
            log.error("[防火门异常列表] 查询参数不能为空");
            throw new IllegalArgumentException("查询参数不能为空");
        }

        if (queryData.getParams() == null) {
            log.error("[防火门异常列表] 查询参数Map不能为空");
            throw new IllegalArgumentException("查询参数Map不能为空");
        }

        // 校验分页参数
        if (queryData.getPageNo() == null || queryData.getPageNo() <= 0) {
            log.error("[防火门异常列表] 页码必须大于0，当前值: {}", queryData.getPageNo());
            throw new IllegalArgumentException("页码必须大于0");
        }

        if (queryData.getPageSize() == null || queryData.getPageSize() <= 0) {
            log.error("[防火门异常列表] 每页大小必须大于0，当前值: {}", queryData.getPageSize());
            throw new IllegalArgumentException("每页大小必须大于0");
        }

        log.info("[防火门异常列表] 参数校验通过");
    }

    /**
     * 构建异常查询条件
     *
     * @param buildingId 建筑物ID
     * @param params 查询参数Map
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<StatFireDoorExpection> buildExceptionQueryWrapper(String buildingId, Map<String, String> params) {
        LambdaQueryWrapper<StatFireDoorExpection> wrapper = new LambdaQueryWrapper<>();

        // 必填条件：建筑ID
        wrapper.eq(StatFireDoorExpection::getBuildingId, buildingId);
        log.info("[防火门异常列表] 添加建筑ID筛选: {}", buildingId);

        // 可选条件：设备类型
        addDeviceTypeCondition(wrapper, params);

        // 可选条件：异常类型
        addExceptionTypeCondition(wrapper, params);

        // 排序：按异常上报时间倒序（最新的异常在前）
        wrapper.orderByDesc(StatFireDoorExpection::getReportTime);

        return wrapper;
    }

    /**
     * 添加设备类型查询条件
     *
     * @param wrapper 查询条件包装器
     * @param params 查询参数Map
     */
    private void addDeviceTypeCondition(LambdaQueryWrapper<StatFireDoorExpection> wrapper, Map<String, String> params) {
        String devType = params.get("devType");
        if (StrUtil.isNotBlank(devType)) {
            wrapper.eq(StatFireDoorExpection::getDevType, devType);
            log.info("[防火门异常列表] 添加设备类型筛选: {}", devType);
        }
    }

    /**
     * 添加异常类型查询条件
     *
     * @param wrapper 查询条件包装器
     * @param params 查询参数Map
     */
    private void addExceptionTypeCondition(LambdaQueryWrapper<StatFireDoorExpection> wrapper, Map<String, String> params) {
        String expectionType = params.get("expectionType");
        if (StrUtil.isNotBlank(expectionType)) {
            wrapper.eq(StatFireDoorExpection::getExpectionType, expectionType);
            log.info("[防火门异常列表] 添加异常类型筛选: {} (1报警,2故障,3离线,4其他)", expectionType);
        }
    }

    /**
     * 处理防火门设备异常
     *
     * @param exceptionId 异常ID，多个用逗号分隔
     * @param request HTTP请求
     * @return 处理结果
     */
    @Transactional
    @Override
    public JsonResult editFireDevice(String exceptionId, Map<String, String> requestData, HttpServletRequest request) {
        log.info("[处理防火门异常] 开始处理异常，异常ID: {}", exceptionId);

        // 从requestData中获取参数
        String handlingInfo = requestData.get("handlingInfo");
        String picUrls = requestData.get("picUrls");

        // 参数校验
        checkParameter(exceptionId, handlingInfo);

        try {
            // 批量处理异常
            String[] exceptionIds = exceptionId.split(",");
            for (String id : exceptionIds) {
                processException(id.trim(), handlingInfo, picUrls, request);
            }

            log.info("[处理防火门异常] 处理完成，共处理{}个异常", exceptionIds.length);
            return JsonResult.getSuccessResult("处理成功");

        } catch (Exception e) {
            log.error("[处理防火门异常] 处理异常失败，异常ID: {}", exceptionId, e);
            throw new RuntimeException("处理防火门异常失败", e);
        }
    }

    /**
     * 参数校验
     * @param exceptionId
     * @param handlingInfo
     */
    private static void checkParameter(String exceptionId, String handlingInfo) {
        // 参数校验
        if (StrUtil.isBlank(exceptionId)) {
            log.error("[处理防火门异常] 异常ID不能为空");
            throw new IllegalArgumentException("异常ID不能为空");
        }

        if (StrUtil.isBlank(handlingInfo)) {
            log.error("[处理防火门异常] 处理信息不能为空");
            throw new IllegalArgumentException("处理信息不能为空");
        }
    }

    /**
     * 处理单个异常
     */
    private void processException(String exceptionId, String handlingInfo, String picUrls, HttpServletRequest request) {
        // 查询异常记录
        StatFireDoorExpection exception = fireDoorExceptionMapper.selectById(exceptionId);
        if (exception == null) {
            log.warn("[处理防火门异常] 异常记录不存在，异常ID: {}", exceptionId);
            throw new IllegalArgumentException("异常记录不存在: " + exceptionId);
        }

        // 直接更新异常状态，不再检查设备实时状态
        updateFireException(exception, handlingInfo, picUrls, request);
        log.info("[处理防火门异常] 异常处理完成，异常ID: {}", exceptionId);
    }

    /**
     * 修改异常状态
     * @param statFireDoorExpection 异常记录
     * @param handlingInfo 处理信息
     * @param picUrls 反馈图片
     * @param request HTTP请求
     */
    private void updateFireException(StatFireDoorExpection statFireDoorExpection, String handlingInfo, String picUrls, HttpServletRequest request) {
        UsersResponse.DataDTO dataDTO = orgMiddleService.findUserByUserId(request.getHeader("Wzt-Userid"));

        // 设置异常处理信息
        statFireDoorExpection.setExpectionStatus("1");
        statFireDoorExpection.setHandlingInfo(handlingInfo);
        statFireDoorExpection.setHandlingId(dataDTO.getUserId());
        statFireDoorExpection.setHandlingName(dataDTO.getFullName());
        statFireDoorExpection.setHandlingTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));

        // 设置反馈图片信息
        if (StrUtil.isNotBlank(picUrls)) {
            statFireDoorExpection.setPicUrls(Integer.valueOf(picUrls));
        }

        fireDoorExceptionMapper.updateById(statFireDoorExpection);
    }

    /**
     * BaseBuilding和StatFireDoorExpection联动查询
     * @param queryData 查询条件
     * @return JsonPageResult 联动查询结果
     */
    @Override
    public JsonPageResult buildingFireDoorQuery(QueryData queryData) {
        log.info("[防火门联动查询] 开始执行联动查询，查询参数: {}", queryData);

        try {
            // 构建分页参数
            Page<Map<String, Object>> page = new Page<>(queryData.getPageNo(), queryData.getPageSize());

            // 构建查询参数
            Map<String, Object> params = new HashMap<>();
            if (queryData.getParams() != null) {
                params.putAll(queryData.getParams());
            }

            // 执行联动查询
            Page<Map<String, Object>> resultPage = fireDoorExceptionMapper.buildingFireDoorQuery(page, params);

            log.info("[防火门联动查询] 查询完成，共查询到 {} 条记录", resultPage.getTotal());

            return new JsonPageResult(resultPage);

        } catch (Exception e) {
            log.error("[防火门联动查询] 查询异常: ", e);
            return JsonPageResult.getFail("查询失败: " + e.getMessage());
        }
    }


}
